
import clientStatusEnum from '@/enums/client-status-enum';
import clientTypesEnum from '@/enums/client-types-enum';

export default function clientsSelectTableItems(t, detachClient) {

            const {
                cases: clientStatusCases,
                enumData: clientStatusEnumData
            } = clientStatusEnum();
        
            const {
                cases: clientTypesCases,
                enumData: clientTypesEnumData
            } = clientTypesEnum();
    const cols = [
        { orderable: false ,header: 'username', field: 'clients.username', cell: (item) => item.username },
        { orderable: false ,header: 'email', field: 'clients.email', cell: (item) => item.email },
        { orderable: false ,header: 'country', field: 'clients.country', cell: (item) => item.country },
        { orderable: false ,header: 'status', field: 'clients.status', cell: (item) => clientStatusEnumData[item.status][`title_${t("locale.lang")}`] },
        { orderable: false ,header: 'type', field: 'clients.type', cell: (item) => clientTypesEnumData[item.type][`title_${t("locale.lang")}`] },

    ];

    const actions = [
        {
            header: 'delete',
            perm: 'groups/delete',
            icon: "mdi-delete",
            action: (item) => detachClient(item.id)
        }
    ];


    return {
        cols,
        actions
    }
}