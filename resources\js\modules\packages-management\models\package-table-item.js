import PackageStatusEnum from "@/enums/package-status-enum";
import PackageUnitEnum from "@/enums/package-unit-enum";

export default function PackageTableItems(t, deleteItem, redirect,disablePackage) {
    const { cases: statusCases, enumData: statusEnumData } = PackageStatusEnum();
    const { enumData: unitEnumData } = PackageUnitEnum();

    const cols = [
        { header: 'name', field: 'packages.name', cell: (item) => item.name },
        { header: 'duration', field: 'packages.duration', cell: (item) => item.duration },
        { header: 'unit', field: 'packages.unit', cell: (item) => unitEnumData[item.unit]?.[`title_${t("locale.lang")}`] || item.unit },
        { header: 'status', field: 'packages.status', cell: (item) => statusEnumData[item.status]?.[`title_${t("locale.lang")}`] || item.status },
        { header: 'price', field: 'packages.price', cell: (item) => item.price },
    ];

    const actions = [
        {
            header: 'details',
            perm: 'packages/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('packages/details', { id: item.id })
        },
        {
            header: 'update',
            perm: 'packages/update',
            icon: "mdi-pencil",
            action: (item)  => redirect('packages/update', { id: item.id }),
            visible: (item) => item.visible_button
        },
        {
            header: 'delete',
            perm: 'packages/delete',
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id,false,'packages'),
            visible: (item) => item.visible_button
        },
        {
            header: 'disable',
            perm: 'packages/disable',
            icon: "mdi-stop",
            action: (item) => disablePackage(item.id),
            visible: (item) => item.status === statusCases.AVAILABLE
        }
    ];

    return {
        cols,
        actions
    }
}
