import BaseService from "./base-service";
import axios from "axios";
import authHeader from "./auth-header";

class GroupService extends BaseService {
    routPath = '/groups';

    constructor() {
        super();
    }

    clients(params, showLoader) {
        return axios.get(this.routPath + '-clients' + '?page=' + params.page +
                    "&limit=" + params.size + "&search=" + params.search
                    , {headers: authHeader(), showLoader: showLoader}
            , {headers: authHeader()}
        );
    }

    groupClients(params, showLoader) {
        return axios.get(this.routPath + '-group-clients' + '?page=' + params.page +
                    "&limit=" + params.size + "&search=" + params.search + "&group_id=" + params.parent_id
                    , {headers: authHeader(), showLoader: showLoader}
            , {headers: authHeader()}
        );
    }

    detachClient(params, showLoader) {
        return axios.post(this.routPath + '-detach-client/' + params.group_id
            , { client_id:params.client_id }
            , {headers: authHeader()}
        );
    }
}

export default new GroupService();
