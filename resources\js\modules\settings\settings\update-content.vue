<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black text-h5">
                {{ $t('settings.edit') }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <v-form v-model="valid" v-on:submit.prevent="saveContent">

            <v-row>
                <v-col>
                    <v-label class="text-subtitle-1 font-weight-medium">
                        {{ $t("settings.name") + " : " + itemData.name }}
                    </v-label>
                </v-col>
            </v-row>


            <v-row class="mt-3">
                <v-col>
                    <QuillEditor
                        v-if="itemData.type === 'text'"
                        v-model:content="itemData.value"
                        content-type="html"
                        theme="snow"
                        class="custom-editor"
                    />
                </v-col>
            </v-row>

            <v-row class="mt-10">
                <v-col class="d-flex justify-end">
                    <v-btn class="colored-btn px-4 py-2" type="submit">
                        <span class="px-2">{{ $t('edit') }}</span>
                    </v-btn>

                    <router-link :to="{ name: 'settings/indexContent'}">
                        <v-btn class="colored-btn-cancel mx-2 px-4 py-2">
                            {{ $t('cancel') }}
                        </v-btn>
                    </router-link>

                </v-col>
            </v-row>

        </v-form>
    </v-container>
</template>

<script setup>
import { onMounted } from 'vue';
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import useSettings from "@/modules/settings/composables/settings";

const {
    router,
    isLoading,
    valid,
    updateItem,
    itemData,
    getItem,
} = useSettings();

const props = defineProps({
    id: {
        required: true,
        type: String
    }
});

onMounted(() => {
    getItem(props.id, true);
});
const saveContent = async () => {
    if (!itemData.value || itemData.value.value.trim() === '') {
        valid.value = false;
        notify('Content cannot be empty');
        return;
    }
    valid.value = true;
    await updateItem(itemData.value, 'settings/indexContent', true);
};
</script>
