import BaseService from "../base-service";
import axios from "axios";
import authHeader from "../auth-header";

class SubscriptionService extends BaseService {
    routPath = '/subscriptions';

    constructor() {
        super();
    }


    index(params, showLoader) {
        return axios.get(this.routPath + '?package_id=' + params.package_id +'&client_id=' + params.client_id +
            '&page=' + params.page +
            '&sort=' + params.sort +
            "&limit=" + params.size +
            "&search=" + params.search
            , {headers: authHeader(), showLoader}
        );
    }

    getPackages() {
        return axios.get('get-packages', {headers: authHeader()});
    }

    disable(id) {
        return axios.post(this.routPath + '/disable/' + id, {}, {headers: authHeader()});    }
}

export default new SubscriptionService();
