<?php

use App\Http\Middleware\GuardSwitcher;
use App\Jobs\CheckServerUpdatesJob;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Traits\MobileResponse;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Database\RecordsNotFoundException;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\File\Exception\FileNotFoundException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\Routing\Exception\RouteNotFoundException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware(['api', 'guardswitcher:client'])
                ->prefix('client')
                ->group(base_path('routes/mobile.php'));
            Route::middleware(['api'])
                ->prefix('vpn')
                ->group(base_path('routes/vpn.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'guardswitcher' => GuardSwitcher::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class
        ]);
        $middleware->use([
            \App\Http\Middleware\Localization::class,
            \Illuminate\Foundation\Http\Middleware\TrimStrings::class,
        ]);
    })
    ->withSchedule(function (Schedule $schedule) {
        $schedule->call(function () {
            CheckServerUpdatesJob::dispatch();
        })->everyFiveMinutes();
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (Exception $exception, Request $request) {
            if ($request->route() && $request->route()->getPrefix() === "client" && config('app.env') !== 'development') {
                switch ($exception) {
                    case $exception instanceof AuthenticationException:
                        return MobileResponse::MobileResponse($exception->getMessage(), code: Response::HTTP_UNAUTHORIZED);
                    case $exception instanceof AuthorizationException:
                        return MobileResponse::MobileResponse('failed', code: Response::HTTP_FORBIDDEN);
                    case $exception instanceof RecordsNotFoundException
                        or $exception instanceof NotFoundHttpException
                        or $exception instanceof FileNotFoundException
                        or $exception instanceof RouteNotFoundException:
                        return MobileResponse::MobileResponse($exception->getMessage(), code: Response::HTTP_NOT_FOUND);
                    case $exception instanceof ValidationException:
                        return MobileResponse::MobileResponse(__('failed'),$exception->errors(), code: Response::HTTP_UNPROCESSABLE_ENTITY);
                    default:
                        return MobileResponse::MobileResponse($exception->getMessage(), code: Response::HTTP_INTERNAL_SERVER_ERROR);
                }
            }
        });
    })->create();
