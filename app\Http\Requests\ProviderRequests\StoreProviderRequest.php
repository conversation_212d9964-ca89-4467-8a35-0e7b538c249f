<?php

namespace App\Http\Requests\ProviderRequests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreProviderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'           => ['required', 'string', 'max:100',
                Rule::unique('providers')->whereNull('deleted_at')],
            'contact_number' => ['nullable', 'string', 'max:50'],
            'email'          => ['nullable', 'string', 'email', 'max:100'],
            'website'        => ['nullable', 'string', 'url', 'max:255'],
            'admin_url'      => ['nullable', 'string', 'url', 'max:255'],
            'note'           => ['nullable', 'string'],
        ];
    }
}
