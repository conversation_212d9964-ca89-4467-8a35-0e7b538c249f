<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Traits\LogColumns;
use \App\Enums\NotificationsStatusEnum;
use \App\Enums\NotificationsTypeEnum;

return new class extends Migration
{
    use LogColumns;
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->enum('status',NotificationsStatusEnum::asArray());
            $table->unsignedBigInteger('group_id')->nullable();
            $table->foreign('group_id')->references('id')->on('groups');
            $table->string('subject', 50);
            $table->text('content');
            $table->timestamp('scheduled_at');
            $table->enum('type',NotificationsTypeEnum::asArray());
            $this->LogColumns($table);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
