<?php

namespace App\Http\Requests\PackageRequests;

use App\Enums\PackageStatusEnum;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class DisablePackageRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status'   => ['sometimes', Rule::in(PackageStatusEnum::asArray())],
        ];
    }
}
