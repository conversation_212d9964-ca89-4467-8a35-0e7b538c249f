import axios from "axios";
import BaseService from "./base-service";
import authHeader from "./auth-header";

class ServersService extends BaseService {
    routPath = '/servers';

    constructor() {
        super();
    }

    providers(showLoader) {
        return axios.get(this.routPath + '-providers'
            , {headers: authHeader(), showLoader}
        );
    }

    regenerateToken(id, showLoader) {
        return axios.post(this.routPath + '/regenerate-token/' + id
            , {}
            , {headers: authHeader(), showLoader}
        );
    }
}

export default new ServersService();
