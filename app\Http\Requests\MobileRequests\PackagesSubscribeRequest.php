<?php

namespace App\Http\Requests\MobileRequests;

use App\Http\Requests\BaseRequest;
use App\Rules\IsPackageAvailable;

class PackagesSubscribeRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'package_id' => ['required', 'integer', 'min:1', 'exists_ignore_deleted:packages,id', new IsPackageAvailable()],
            
        ];
    }
}
