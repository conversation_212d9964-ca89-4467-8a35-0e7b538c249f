<template>
    <v-container>
        <v-card width="90%" class="pa-5 mx-auto" color="white" outlined>
            <v-card-title class="headline black" primary-title>
                <h3>
                    {{ $t('tickets.Handle') }}
                </h3>
            </v-card-title>
            <v-card-text class="pa-5">
                <v-form
                    v-model="valid"
                    v-on:submit.prevent="emit('update', {...updateForm}, 'tickets', true)"
                >

                   <v-row class="mt-n2">
                        <v-col>
                            <v-textarea
                                v-model="updateForm.note"
                                :label="$t('tickets.note')"
                                variant="outlined"
                                :rules="validation.note"
                                class="required"
                            >
                            </v-textarea>
                        </v-col>

                    </v-row>
                    <v-btn
                        @click="emit('cancel')"
                        :class="'float-' + $t('right')"
                        class="colored-btn-cancel"
                    >
                        {{ $t("cancel") }}
                    </v-btn>
                    <v-btn
                        :class="'float-' + $t('right') + ' colored-btn'"
                        type="submit"
                    >
                        <span class="px-2">{{ $t("edit") }}</span>
                    </v-btn>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>
<script setup>
import {ref, defineProps} from 'vue';

const valid = defineModel("valid", {type: Boolean});
const form = defineModel("form", {type: Object});
const props = defineProps(["validation"]);
const emit = defineEmits(["cancel", "update"]);

const updateForm = ref({...form.value});


</script>

