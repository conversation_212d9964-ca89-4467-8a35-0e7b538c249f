import {ref} from 'vue';
import useShared from "@/helpers/shared.js";
import statisticsService from '@/services/statistics-service';
import { useIntervalFn } from '@vueuse/core'

export default function useClients() {
    const { errorHandle } = useShared();

    const statistics = ref({
        total_users: 0,
        free_users: 0,
        paid_users: 0,
        logged_in_users: {
            today: 0,
            last_7_days: 0,
            last_30_days: 0
        },
        new_users: 0,
        user_distribution: [],
        conversion_rate: 0
    });

    const isLoading = ref(false);

    const fetchStatistics = async () => {
        isLoading.value = true;
        try {
            const response = await statisticsService.clients();
            statistics.value = response.data.data;
        } catch (error) {
            errorHandle(error);
        } finally {
            isLoading.value = false;
        }
    };

    const { pause, resume, isActive } = useIntervalFn(fetchStatistics, 20000);
    const resetInterval = () => {
        pause();
        resume();
    };

    const reloadData = () => {
        resetInterval();
        fetchStatistics();
    }

    return {
        reloadData,
        statistics,
        isLoading,
        fetchStatistics
    };
}