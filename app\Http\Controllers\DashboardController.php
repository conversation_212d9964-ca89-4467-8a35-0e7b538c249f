<?php

namespace App\Http\Controllers;

use App\Enums\ClientNotificationsStatusEnum;
use App\Enums\NotificationsStatusEnum;
use App\Enums\ServerInternalStatus;
use App\Models\ClientNotification;
use App\Models\Expense;
use App\Models\Notification;
use App\Models\Package;
use App\Models\Server;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function subscriptions()
    {
        $subscriptions = Subscription::query();

        $data['this_month'] = $subscriptions
            ->clone()
            ->duringMonthOffset(0)
            ->count();

        $data['last_month'] = $subscriptions
            ->clone()
            ->duringMonthOffset(1)
            ->count();

        $data['before_last_month'] = $subscriptions
            ->clone()
            ->duringMonthOffset(2)
            ->count();

        $data['package_subscribers_count'] = Package::withCount('subscriptions')
            ->get()
            ->map(function ($package) {
                return [
                    'name' => $package['name'],
                    'subscriptions_count' => $package['subscriptions_count']
                ];
            });

        return $data;
    }

    public function expenses()
    {
        $data = [];

        $expenses = Expense::query();

        $data['total'] = $expenses->clone()->sum('amount');

        $data['total_this_month'] = $expenses
            ->clone()
            ->duringMonthOffset(0)
            ->sum('amount');

        $data['total_last_month'] = $expenses
            ->clone()
            ->duringMonthOffset(1)
            ->sum('amount');

        return $data;
    }

    public function notifications()
    {
        $data = [];

        $notifications = ClientNotification::query();

        $data['sending'] = $notifications
            ->clone()
            ->where('status', ClientNotificationsStatusEnum::Sending->value)
            ->count();

        $data['sent'] = $notifications
            ->clone()
            ->where('status', ClientNotificationsStatusEnum::Sent->value)
            ->count();

        return $data;
    }

    public function servers()
    {
        $servers = Server::query();

        $totalServers = $servers
            ->clone()
            ->count();

        $activeServers = $servers
            ->clone()
            ->where('internal_status', ServerInternalStatus::ENABLED->value)
            ->count();

        $freeServers = $servers
            ->clone()
            ->free()
            ->count();

        $premiumServers = $servers
            ->clone()
            ->premium()
            ->count();

        return [
            'total_servers' => $totalServers,
            'active_servers' => $activeServers,
            'free_servers' => $freeServers,
            'premium_servers' => $premiumServers
        ];
    }
}
