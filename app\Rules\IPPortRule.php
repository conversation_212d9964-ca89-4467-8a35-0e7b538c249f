<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Validator;

class IPPortRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if($value){
            if (str_contains($value, '[') && str_contains($value, ']:')) {
            // Handle IPv6 format [IPv6]:port
            $parts = explode(']:', $value);
            if (count($parts) !== 2) {
                $fail('The ' . $attribute . ' must be in the format [ipv6]:port.');
                return;
            }
            $ip = trim(substr($parts[0], 1));
            $port = trim($parts[1]);
        } else {
            // Handle IPv4 format
            $parts = explode(':', $value);
            if (count($parts) !== 2) {
                $fail('The ' . $attribute . ' must be in the format ip:port.');
                return;
            }
            $ip = trim($parts[0]);
            $port = trim($parts[1]);
        }
        

        $validator = Validator::make(['ip' => $ip, 'port' => $port], [
            'ip' => 'required|ip',
            'port' => 'required|integer|min:1|max:65535',
        ]);

        if ($validator->fails()) {
            $fail($validator->errors()->first());
        }
        }

    }
}
