<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="
                itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)
            "
        >
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("protocols.details") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-row class="mt-n3">
            <v-col cols="2">
                <v-label>
                    {{ $t("protocols.name") }}
                </v-label>
            </v-col>
            <v-col cols="10">
                <b>
                    {{ itemData.name }}
                </b>
            </v-col>
            <v-col cols="2">
                <v-label>
                    {{ $t("protocols.code") }}
                </v-label>
            </v-col>
            <v-col cols="10">
                <b>
                    {{ itemData.code }}
                </b>
            </v-col>
            <v-col cols="2">
                <v-label>
                    {{ $t("protocols.version") }}
                </v-label>
            </v-col>
            <v-col cols="10">
                <b>
                    {{ itemData.version }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-5">
            <v-col cols="2">
                <v-label>
                    {{ $t("protocols.template") }}
                </v-label>
            </v-col>
            <v-col cols="10">
                <v-textarea
                        v-model="itemData.template"
                        variant="outlined"
                        readonly
                        class="json-template"
                    ></v-textarea>
            </v-col>
        </v-row>
    </v-container>
</template>

<script setup>
import { onMounted } from "vue";
import TDataTable from "@/shared/components/t-data-table.vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import useProtocols from "../composables/protocols.js";

const {
    getItem,
    itemData,
    isLoading,
    permissions,
    loadParentData,
    tableData,
    query,
    pagination,
    parent,
    router,
    userPermissions,
    roleCols,
} = useProtocols();
const props = defineProps({
    id: {
        required: true,
        type: String,
    },
});

onMounted(async () => {
    parent.value = props.id;
    getItem(props.id, true);
});
</script>
