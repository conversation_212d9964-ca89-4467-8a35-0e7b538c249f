<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="
                itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)
            "
        >
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("servers.details") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <main-data :itemData="itemData" :regenerateToken="regenerateToken" />
        <details-tabs 
        :itemData="itemData" 
        :id="id" 
        
        />

    </v-container>
</template>

<script setup>
import useServers from "../composables/servers.js";
import { onMounted } from "vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import MainData from "./details/main.vue";
import DetailsTabs from "./details/tabs.vue";

const {
    getItem,
    itemData,
    isLoading,
    router,
        
    userPermissions,
    regenerateToken
} = useServers();

const props = defineProps({
    id: {
        required: true,
        type: String,
    },
});

onMounted(async () => {
    // Load server data with resources
    await getItem(props.id, true);
});
</script>
