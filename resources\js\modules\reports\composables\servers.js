import { reactive, ref } from 'vue'
import useShared from "@/helpers/shared.js";
import serverTableItems from '../models/server-table-items';
import reportsService from '@/services/reports-service';
import { useIntervalFn } from '@vueuse/core'

export default function useServers() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        storeItem,
        loadData,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadParentData,
        deleteItem,
        errorHandle,
        cancel,
        saveItem,
        router,
        userPermissions,
        t,
        cookie,
        redirect
    } = useShared()

    service.value = reportsService;

    const server = ref();

    const {
        cols: serverCols,
    } = serverTableItems(t, redirect, showUpdateModal);

    const getServers = async (query, showLoader = false) => {
        try {
            isLoading.value = true;
            if (query === undefined)
                query = {
                    search: '',
                    page: 1,
                    sort: '',
                    per_page: 10,
                }
            const { data: { data, meta } } = await service.value.getServers({
                parent_id: '',
                page: query.page,
                sort: query.sort,
                size: query.per_page,
                search: query.search,
            }, showLoader);
            tableData.value = data
            pagination.value = { ...pagination.value, page: query.page, total: meta.total, per_page: query.per_page };
            cookie.set(`${service.value.routPath}LoadData`, JSON.stringify({ pagination: pagination.value, query: query }));
            isLoading.value = false
        } catch (error) {
            isLoading.value = false
            await errorHandle(error)
        }
    }

    const { pause, resume, isActive } = useIntervalFn(getServers, 20000);

    const resetInterval = () => {
        pause();
        resume();
    };

    const reloadData = () => {
        resetInterval();
        getServers(false);
    };

    return {
        itemData,
        tableData,
        pagination,
        query,
        server,
        isLoading,
        parent,
        updateModal,
        storeModal,
        getItem,
        loadData,
        service,
        loadParentData,
        storeItem,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        serverCols,
        getServers,
        reloadData,
    }
}
