<?php

use App\Enums\ServerResources;
use App\Traits\LogColumns;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    use LogColumns;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('server_resources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('server_id');
            $table->enum('resource', ServerResources::asArray());
            $table->decimal('value', 7, 2);
            $table->decimal('consumption', 7, 2)->nullable();
            $table->decimal('threshold', 5, 2);
            $this->LogColumns($table);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('server_resources');
    }
};
