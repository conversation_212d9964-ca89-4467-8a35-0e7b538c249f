<?php

namespace App\Http\Controllers\MobileControllers\Auth;

use App\Enums\ClientStatusEnum;
use App\Enums\ClientTypesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\MobileRequests\ChangeMyPasswordRequest;
use App\Http\Requests\MobileRequests\DeleteMyAccountRequest;
use App\Http\Requests\MobileRequests\LoginRequest;
use App\Http\Requests\MobileRequests\RegisterRequest;
use App\Http\Requests\MobileRequests\ResendVerificationCodeRequest;
use App\Http\Requests\MobileRequests\ResetPasswordRequest;
use App\Http\Requests\MobileRequests\SendResetPasswordRequest;
use App\Http\Requests\MobileRequests\ValidateResetCodeAndLoginRequest;
use App\Http\Requests\MobileRequests\VerifyEmailRequest;
use App\Models\Client;
use Illuminate\Auth\AuthenticationException;
use App\Services\MobileServices\AuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthenticationController extends Controller
{
    protected $service;

    public function __construct()
    {
        $this->service = new AuthService();
    }

    /**
     * @param LoginRequest $request
     * @return false|string
     * @throws ValidationException
     */
    public function login(LoginRequest $request)
    {
        $client = Client::whereEmail($request->email)
            ->whereStatus(ClientStatusEnum::ACTIVE->value)
            ->first();

        if ($client && Hash::check($request->validated('password'), $client['password'])) {
            $res = $client->tokenize();
            return self::MobileResponse('success', $res);
        }
        throw new AuthenticationException('invalid_email_or_password');
    }

    /**
     *
     */
    public function register(RegisterRequest $request)
    {
        $this->service->register($request);
        return self::MobileResponse('success');
    }

    public function verifyEmail(VerifyEmailRequest $request)
    {
        $client = Client::where('email', $request->validated('email'))->firstOrFail();
        $res = $this->service->verifyEmail($request, $client);
        return self::MobileResponse('success', $res);
    }

    public function resendVerificationCode(ResendVerificationCodeRequest $request)
    {
        $client = Client::where('email', $request->validated('email'))->firstOrFail();
        $this->service->sendVerificationCode($client);
        return self::MobileResponse('success');
    }

    public function sendResetPasswordCode(SendResetPasswordRequest $request)
    {
        $client = Client::whereEmail($request->email)
            ->whereStatus(ClientStatusEnum::ACTIVE->value)
            ->firstOrFail();
        $this->service->sendResetPasswordCode($client);
        return self::MobileResponse('success');
    }

    public function validateResetCodeAndLogin(ValidateResetCodeAndLoginRequest $request)
    {
        $client = Client::whereEmail($request->email)
            ->whereStatus(ClientStatusEnum::ACTIVE->value)
            ->firstOrFail();
        
        $res = $this->service->validateResetCodeAndLogin($request, $client);
        return self::MobileResponse('success', $res);
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $client = auth()->user();
        $this->service->resetPassword($request, $client);
        return self::MobileResponse('success');
    }

    public function changeMyPassword(ChangeMyPasswordRequest $request)
    {
        $client = auth()->user();
        $this->service->changePassword($request->validated('old_password'), $request->validated('password'), $client);
        return self::MobileResponse('success');
    }

    /**
     * @param Request $request
     * @return false|string
     */
    public function logout(Request $request)
    {
        $client = auth()->user();
        $client->deleteCurrentToken();
        return self::MobileResponse('success');
    }

    public function deleteMyAccount(DeleteMyAccountRequest $request)
    {
        $client = auth()->user();
        $this->service->deleteMyAccount($request, $client);
        return self::MobileResponse('success');
    }

    public function detectCountry()
    {
        $userIp = request()->ip();
        $currentCountry = geoip($userIp);
        return self::MobileResponse('success', [
            'country_code' => $currentCountry['country_code2'],
            'ip' => $userIp,
        ]);

    }
}
