<?php

use App\Enums\ServerExternalStatus;
use App\Enums\ServerHealthStatus;
use App\Enums\ServerInternalStatus;
use App\Traits\LogColumns;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    use LogColumns;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('servers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('location_id')->constrained();
            $table->foreignId('provider_id')->constrained();
            $table->string('name', 50);
            $table->string('ip', 50);
            $table->string('socket_port', 4);
            $table->enum('internal_status', ServerInternalStatus::asArray())->default(ServerInternalStatus::DISABLED);
            $table->enum('external_status', ServerExternalStatus::asArray())->default(ServerExternalStatus::UP);
            $table->enum('health_status', ServerHealthStatus::asArray())->default(ServerHealthStatus::HEALTHY);
            $table->decimal('cost', 6, 2);
            $table->boolean('is_free');
            $this->LogColumns($table);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('servers');
    }
};
