<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container>
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t('notifications.add') }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <v-form v-model="valid" v-on:submit.prevent="saveNotification">
            <v-row class="mt-n5">
                <v-col>
                    <v-text-field
                        v-model="form.subject"
                        :label="$t('notifications.subject')"
                        variant="solo"
                        :rules="validation.subject"
                        class="required"
                    >
                    </v-text-field>
                </v-col>
            </v-row>
            <v-row class="mt-n5">
                <v-col>
                    <v-textarea
                        dense
                        v-model="form.content"
                        :label="$t('notifications.content')"
                        variant="solo"
                        :rules="validation.content"
                    >
                    </v-textarea>
                </v-col>
            </v-row>

            <v-row class="mt-n5">
                <v-col>
                    <v-select
                        :label="$t('notifications.type')"
                        v-model="form.type"
                        :items="notificationsTypes"
                        :item-title="'title_' + $t('locale.lang')"
                        item-value="value"
                        variant="solo"
                        :rules="validation.content"
                        clearable

                    >
                    </v-select>
                </v-col>
            </v-row>

            <v-row class="mt-n5" v-if="form.type === notificationTypeEnum().cases.ONE">
                <v-col>
                    <t-lazy-select
                        :label="$t('notifications.clients')"
                        v-model:selectedItems="form.client_id"
                        :itemTitle="'dropdown_name'"
                        :uri="`/notifications-clients`"
                        :itemValue="'id'"
                        :variant="'solo'"
                        :serverSearch="true"
                        :requestOnClick="true"
                        :class="'text-' + $t('right')"
                        :validation="validation.client_id"
                        clearable
                        />
                </v-col>
            </v-row>

            <v-row class="mt-n5" v-if="form.type === notificationTypeEnum().cases.GROUP">
                <v-col>
                    <v-select
                        :label="$t('notifications.group')"
                        v-model="form.group_id"
                        :items="groups"
                        item-title="name"
                        item-value="id"
                        variant="solo"
                        :rules="validation.group_id"
                        clearable
                    >
                    </v-select>
                </v-col>
            </v-row>

            <v-row class="mt-n5">
                <v-col>
                    <v-text-field
                        v-model="form.scheduled_at"
                        :type="'datetime-local'"
                        :label="$t('notifications.scheduled_at')"
                        :rules="validation.scheduled_at"
                        variant="solo"
                    >
                    </v-text-field>
                </v-col>
            </v-row>

            <router-link :to="{ name: 'notifications'}">
                <v-btn :class=" 'float-'+$t('right') " class="colored-btn-cancel">
                    {{ $t('cancel') }}
                </v-btn>
            </router-link>
            <v-btn :class="'float-'+$t('right') + ' colored-btn'"
                   type="submit"
            >
                <span class="px-2">{{ $t('save') }}</span>
                <img class="crud-icon" src="@/assets/icons/ic_add_2.svg">
            </v-btn>
        </v-form>

    </v-container>
</template>
<script setup>
import {onMounted } from 'vue';
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import useNotifications from "@/modules/notifications-management/composables/notifications";
import notificationTypeEnum from "@/enums/notification-type-enum";
import TLazySelect from "@/shared/components/t-lazy-select.vue";

const {
    getNotificationsTypes,
    notificationsTypes,
    router,
    form,
    validation,
    valid,
    storeItem,
    getGroups,
    isLoading,
    groups,
} = useNotifications()

const saveNotification = async () => {
    await storeItem({...form}, 'notifications', true)
}

onMounted(async () => {
    await  getNotificationsTypes();
    await  getGroups();
})

</script>


