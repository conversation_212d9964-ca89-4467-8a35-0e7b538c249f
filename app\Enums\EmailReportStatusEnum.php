<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum EmailReportStatusEnum: string
{
    use EnumTrait;

    case Sent     = 'sent';
    case Failed     = 'failed';


    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }

    const ENUM_DATA = [
        self::Sent->value => [
            'title_en' => 'Sent',
            'title_ar' => 'تم الإرسال',
            'value' => 'sent',
        ],
        self::Failed->value => [
            'title_en' => 'Failed',
            'title_ar' => 'فشل الإرسال',
            'value' => 'failed',
        ],
    ];
}
