
export default function alertTableItems(t, redirect, showUpdateModal) {

    const cols = [
        { header: 'Recipient Email', field: 'reports.alerts.recipient_email', cell: (item) => item.recipient_email },
        { header: 'Subject', field: 'reports.alerts.subject', cell: (item) => item.subject },
        { header: 'Status', field: 'reports.servers.status', cell: (item) => item.status['title_' + t('locale.lang')] },
        { header: 'Error Message', field: 'reports.alerts.error_message', cell: (item) => item.error_message ?? '-' },
        { header: 'Sent At', field: 'reports.alerts.sent_at', cell: (item) => item.sent_at },
    ];

    const actions = [];

    return {
        cols
    }
}
