<?php

namespace App\Http\Resources\ReportResources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $tcpUsers =  $this->tcp_users_sum_current_connections_count;
        $udpUsers = $this->udp_users_sum_current_connections_count;
        $wireguardUsers = $this->wireguard_users_sum_current_connections_count;
        
        return [
            'id' => $this->id,
            'name' => $this->name,
            'status' => $this->translated_internal_status,
            'cpu' => $this->resources_collection['cpu']['consumption'],
            'ram' => $this->resources_collection['ram']['consumption'],
            'disk' => $this->resources_collection['disk']['consumption'],
            'tcp_users' => $tcpUsers,
            'udp_users' => $udpUsers,
            'wireguard_users' => $wireguardUsers,
            'total_users' => $tcpUsers + $udpUsers + $wireguardUsers
        ];
    }
}
