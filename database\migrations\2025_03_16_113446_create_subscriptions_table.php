<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use \App\Traits\LogColumns;
use \App\Enums\SubscriptionStatusEnum;


return new class extends Migration
{
    use LogColumns;
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->references('id')->on('clients');
            $table->foreignId('package_id')->references('id')->on('packages');
            $table->date('from');
            $table->date('to');
            $table->boolean('is_paid');
            $table->enum('status',SubscriptionStatusEnum::getValues());
            $this->LogColumns($table);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
