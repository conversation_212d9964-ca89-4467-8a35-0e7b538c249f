<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("packages.details") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <main-data :itemData="itemData" />
        <!-- Actions Section -->
        <v-row justify="end">
            <div v-for="action in PackageActions">
                <button
                    v-if="action.header !== 'details' && userPermissions.includes(action.perm) && (action.visible ? action.visible(itemData) : true)"
                    :class="action.class" @click="action.action(itemData)">
                    <v-icon :icon="action.icon"></v-icon>
                </button>
            </div>
            </v-row>
        <details-tabs :itemData="itemData" :id="id" />
    </v-container>
</template>

<script setup>
import {onMounted} from "vue";
import usePackages from "../composables/packages.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import MainData from "./details/main.vue";
import DetailsTabs from "./details/tabs.vue";


const {
    t,
    getItem,
    query,
    pagination,
    itemData,
    isLoading,
    router,
    userPermissions,
    updateModal,
    cancel,
    valid,
    validation,
    parent,
    Subscriptions,
    getSubscriptions,
    PackageTableItems,
    deleteItem,
    redirect,
    toggleActivation,
    PackageActions,
} = usePackages();


const props = defineProps({
    id: {
        required: true,
        type: String,
    },
});

onMounted(() => {
    getItem(props.id, true)
})
</script>
