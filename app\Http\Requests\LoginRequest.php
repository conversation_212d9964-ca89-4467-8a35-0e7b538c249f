<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email',
                function ($attribute, $value, $fail) {
                    $user = User::whereEmail($this->email)->whereIsActive(true)
                        ->first();
                    if (!$user)
                        $fail(trans('auth.invalid_credentials'));
                }
            ],
            'password' => ['required'],
        ];
    }
}
