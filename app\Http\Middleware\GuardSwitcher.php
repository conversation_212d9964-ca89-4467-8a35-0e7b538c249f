<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class GuardSwitcher
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, \Closure $next, $defaultGuard = null)
    {
        if (in_array($defaultGuard, array_keys(config("auth.guards")))) {
            config()->set('auth.defaults.guard', $defaultGuard);
         }
         return $next($request);
    }
}
