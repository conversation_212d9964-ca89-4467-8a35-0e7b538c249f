<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServerResourceResource extends JsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'ram' => (double) $this->resources_collection['ram']['value'],
            'cpu' => (double) $this->resources_collection['cpu']['value'],
            'disk' => (double) $this->resources_collection['disk']['value'],
            'ram_threshold' => $this->resources_collection['ram']['threshold'],
            'cpu_threshold' => $this->resources_collection['cpu']['threshold'],
            'disk_threshold' => $this->resources_collection['disk']['threshold'],
            'ram_consumption' => $this->resources_collection['ram']['consumption'],
            'cpu_consumption' => $this->resources_collection['cpu']['consumption'],
            'disk_consumption' => $this->resources_collection['disk']['consumption'],
        ];
    }
}
