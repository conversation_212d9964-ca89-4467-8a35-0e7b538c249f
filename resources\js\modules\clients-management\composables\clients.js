import {nextTick, reactive, ref} from 'vue'
import ClientService from "@/services/clients-service.js";
import useShared from "@/helpers/shared.js";
import clientTableItems from '../models/clients-table-items';
import clientStatusEnum from '@/enums/client-status-enum';
import clientTypesEnum from '@/enums/client-types-enum';
import cookie from "vue-cookies";
import SubscriptionService from "@/services/subscription-management-services/subscription-service.js";
import {notify} from "@kyvg/vue3-notification";

export default function useClients() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        storeItem,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadParentData,
        deleteItem,
        cancel,
        loadData,
        saveItem,
        router,
        userPermissions,
        errorHandle,
        t,
        redirect
    } = useShared()

    service.value = ClientService;

    const {
        cols: clientCols,
        actions: clientActions
    } = clientTableItems(t, deleteItem, redirect);

    const {
        cases: clientStatusCases,
        enumData: clientStatusEnumData
    } = clientStatusEnum();

    const {
        cases: clientTypesCases,
        enumData: clientTypesEnumData
    } = clientTypesEnum();

    const clientTypesItems = Object.values(clientTypesEnumData).filter(item => item.value !== clientTypesCases.GUEST);

    const form = reactive({
        username: '',
        email: '',
        password: '',
        type: clientTypesCases.FREE,
        status: clientStatusCases.ACTIVE,
        registered_at: null,
    });

    const validation = reactive({
        email: [
            validationRules.required,
            validationRules.email,
            validationRules.maxLength(50),
        ],
        password: [
            validationRules.password
        ],
        type: [
            validationRules.required
        ],
        status: [
            validationRules.required
        ],
        registered_at: [
            validationRules.optional
        ],

    })





    const validationSubscription = reactive({
        package_id: [
            validationRules.required,
        ],
    })

    const tabs = ref('devices')

    const devicesTableData = ref([])
    const devicesPagination = ref({})
    const devicesIsLoading = ref(true)

    const subscriptionsTableData = ref([])
    const subscriptionsPagination = ref({})
    const subscriptionsIsLoading = ref(true)
    const packages = ref([]);

    const sessionsTableData = ref([])
    const sessionsPagination = ref({})
    const sessionsIsLoading = ref(true)

    const loadDevicesData = async (devicesQuery) => {
        if(itemData.value){
            try {
                devicesIsLoading.value = true;
                if (devicesQuery === undefined)
                    devicesQuery = {
                        search: '',
                        sort:'',
                        page: 1,
                        per_page: 10,
                    }
                const {data: {data, meta}} = await service.value.devices(itemData.value.id,{
                    page: devicesQuery.page,
                    sort: devicesQuery.sort,
                    size: devicesQuery.per_page,
                    search: devicesQuery.search,
                });
                devicesTableData.value = data
                devicesPagination.value = {...devicesPagination.value, page: devicesQuery.page, total: meta.total, per_page: devicesQuery.per_page}
                cookie.set(`${service.value.routPath}Load${itemData.value.id}DevicesData`, JSON.stringify({pagination: devicesPagination.value, query: devicesQuery}));
                devicesIsLoading.value = false
            } catch (error) {
                devicesIsLoading.value = false
                await errorHandle(error)
            }
        }

    }
    const devicesQuery = ref({
        search: '',
        page: 1,
        sort:'',
        per_page: 10,
    })
    const subscriptionQuery = ref({
        package_id:'',
        search: '',
        page: 1,
        sort:'',
        per_page: 10,
    })

    const loadSubscriptionsData = async (subscriptionQuery) => {
        if (itemData.value) {
            try {

                const {data: {data, meta}} = await SubscriptionService.index({
                    package_id:'',
                    client_id:itemData.value.id,
                    page: subscriptionQuery.page,
                    sort: subscriptionQuery.sort,
                    size: subscriptionQuery.per_page,
                    search: subscriptionQuery.search,
                });
                subscriptionsTableData.value = data
                subscriptionsPagination.value = {
                    ...subscriptionsPagination.value,
                    page: subscriptionQuery.page,
                    total: meta.total,
                    per_page: subscriptionQuery.per_page
                }
                subscriptionsIsLoading.value = false
                cookie.set(`${service.value.routPath}Load${itemData.value.id}SubscriptionsData`, JSON.stringify({
                    pagination: subscriptionsPagination.value,
                    query: subscriptionQuery
                }));
                subscriptionsIsLoading.value = false
            }catch(error)
            {
                subscriptionsIsLoading.value = false
                await errorHandle(error)
            }
        }
    }
    const sessionsQuery = ref({
        search: '',
        page: 1,
        sort:'',
        per_page: 10,
    })

    const loadSessionsData = async (query) => {
        if(itemData.value){
            try {
                sessionsIsLoading.value = true;
                
                const {data: {data, meta}} = await service.value.sessions(itemData.value.id,{
                    page: query.page,
                    sort: query.sort,
                    size: query.per_page,
                    search: query.search,
                });
                sessionsTableData.value = data
                sessionsPagination.value = {...sessionsPagination.value, page: query.page, total: meta.total, per_page: query.per_page}
                cookie.set(`${service.value.routPath}Load${itemData.value.id}SessionsData`, JSON.stringify({pagination: sessionsPagination.value, query: query}));
                sessionsIsLoading.value = false
            } catch (error) {
                sessionsIsLoading.value = false
                await errorHandle(error)
            }
        }
    }

    const getPackages = async () => {
        try {
            const response = await SubscriptionService.getPackages();
            packages.value = response.data.data;
        } catch (error) {
            await errorHandle(error);
        }
    };

    const storeModalItemSubscriptionForClient = async (data, showLoader = false) => {
        try {
            if (itemData.value) {
                data['client_id'] = itemData.value.id;
                let response = await SubscriptionService.store(data, showLoader);
                notify(response.data.message);
                storeModal.value = false;
                await loadSubscriptionsData();
              await getItem(itemData.value.id, true);
              // window.location.reload();
            }
        } catch (error) {
            await errorHandle(error);
        }
    }

    const disableSubscription = async (id) => {
        try {
            let response = await SubscriptionService.disable(id);
            notify(response.data.message);
        } catch (error) {
            await errorHandle(error)
        }
        await loadSubscriptionsData();
        await getItem(itemData.value.id, true);
         // window.location.reload();
    };

    return {
        subscriptionsIsLoading,
        subscriptionsPagination,
        subscriptionsTableData,
        subscriptionQuery,
        loadSubscriptionsData,
        loadDevicesData,
        devicesTableData,
        devicesPagination,
        devicesQuery,
        devicesIsLoading,

        sessionsTableData,
        sessionsPagination,
        sessionsQuery,
        sessionsIsLoading,
        loadSessionsData,

        tabs,

        clientTypesCases,
        validationRules,
        clientTypesItems,
        clientTypesEnumData,
        clientStatusEnumData,
        itemData,
        tableData,
        pagination,
        query,
        isLoading,
        loadData,
        validation,
        form,
        valid,
        parent,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        getItem,
        loadParentData,
        storeItem,
        parentDetails,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        clientCols,
        clientActions,
        redirect,
        t,
        getPackages,
        packages,
        validationSubscription,
        storeModalItemSubscriptionForClient,
        disableSubscription,
    }
}
