<?php

namespace App\Services\MobileServices;

use App\Http\Requests\MobileRequests\ServersToConnectRequest;
use App\Models\Client;
use App\Models\Location;
use App\Models\Port;
use App\Models\Protocol;
use App\Models\Server;

class PortService
{
    public function getTopServers(ServersToConnectRequest $request, Client $client, int $limit)
    {
        $country = $client->currentCountry();
        $freeOnly = $client->isFree();

        $protocolCode = $request->validated('protocol_code');
        $locationCode = $request->validated('location_code');

        $protocolId = null;
        $locationId = null;

        if ($protocolCode) {
            $protocol = Protocol::where('code', $protocolCode)->first();
            $protocolId = $protocol['id'];
        } else if ($country && $country['auto_protocol_id']) {
            $protocolId = $country['auto_protocol_id'];
        }

        if ($locationCode) {
            $location = Location::where('code', $locationCode)->first();
            $locationId = $location['id'];
        } else if ($country && $country['auto_location_id']) {
            $locationId = $country['auto_location_id'];
        }


        $servers = $this->getBestAvailableServers($freeOnly, $protocolId, $locationId);

        if (count($servers) < $limit) {
            $restServers = $this->getBestAvailableServers($freeOnly, null, $locationId);
            $servers = collect($servers)->merge($restServers)->unique('id');

            if (count($servers) < $limit) {
                $restServers = $this->getBestAvailableServers($freeOnly, null, null);
                $servers = collect($servers)->merge($restServers)->unique('id');
            }
        }

        $servers = collect($servers)
            ->take($limit)
            ->values()
            ->map(function ($server, $index) {
                $server['priority'] = $index + 1;
                return $server;
            });

        return $servers;
    }

    public function getBestAvailableServers(bool $freeOnly, $protocolId, $locationId)
    {
        $servers = Port::query()
            ->join('servers', 'ports.server_id', 'servers.id')
            ->join('protocols', 'ports.protocol_id', 'protocols.id')
            ->join('locations', 'servers.location_id', 'locations.id')
            ->leftJoin('locations as parent_locations', 'locations.parent_id', 'parent_locations.id')
            ->availableServers();

        if ($protocolId)
            $servers->filterProtocol($protocolId);

        if ($locationId)
            $servers->filterLocation($locationId);

        if ($freeOnly)
            $servers->free();

        $servers = $servers
            ->orderBy('servers.is_free')
            ->orderBy('current_connections_count')
            ->select(
                'ports.id',
                'servers.name',
                'protocols.code as protocol_code',
                'protocols.version as protocol_version',
                'ports.ip',
                'ports.port',
                'ports.control_ip_port',
                'parent_locations.code as location_code',
            )
            ->get();

        return $servers;
    }
}
