<?php

namespace App\Http\Resources\ReportResources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AlertResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'recipient_email' => $this->recipient_email,
            'subject' => $this->subject,
            'subject' => $this->subject,
            'status' => $this->translated_status,
            'error_message' => $this->error_message,
            'sent_at' => $this->sent_at
        ];
    }
}
