<?php

namespace App\Http\Controllers\MainControllers;

use App\Enums\ServerInternalStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\ReportResources\AlertResource;
use App\Http\Resources\ReportResources\ServerResource;
use App\Models\ClientNotification;
use App\Models\EmailLog;
use App\Models\Server;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    public function getServers(Request $request)
    {
        $query = Server::with('resources')
            ->withSum('tcpUsers', 'current_connections_count')
            ->withSum('udpUsers', 'current_connections_count')
            ->withSum('wireguardUsers', 'current_connections_count');

        return ServerResource::collection($query->paginate($request->limit, '*', 'page', $request->offset));
    }


    public function getAlerts(Request $request)
    {
        $query = EmailLog::orderBy('created_at', 'DESC');

        return AlertResource::collection($query->paginate($request->limit, '*', 'page', $request->offset));
    }
}
