<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PortResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'port' => $this->port,
            'ip' => $this->ip,
            'protocol' => $this->whenLoaded('protocol'),
            'server' => $this->whenLoaded('server'),
            'status' => $this->status,
            'control_ip_port' => $this->control_ip_port,
            'connections_threshold' => $this->connections_threshold,
            'current_connections_count' => $this->current_connections_count,
            'purpose' => $this->purpose,
            'server_public_key' => $this->server_public_key
        ];
    }
}
