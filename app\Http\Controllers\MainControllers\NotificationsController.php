<?php

namespace App\Http\Controllers\MainControllers;

use App\Enums\NotificationsStatusEnum;
use App\Enums\NotificationsTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\NotificationRequests\IndexNotificationRequest;
use App\Http\Requests\NotificationRequests\NotificationClientsRequest;
use App\Http\Requests\NotificationRequests\StoreNotificationRequest;
use App\Http\Requests\NotificationRequests\UpdateNotificationRequest;
use App\Http\Resources\Notification\NotificationResource;
use App\Models\Client;
use App\Models\Group;
use App\Models\Notification;
use Illuminate\Validation\ValidationException;


class NotificationsController extends Controller
{
    function __construct()
    {
        $this->middleware('permission:notifications', ['only' => ['index']]);
        $this->middleware('permission:notifications/create', ['only' => ['store']]);
        $this->middleware('permission:notifications/update', ['only' => ['update']]);
        $this->middleware('permission:notifications/details', ['only' => ['show']]);
        $this->middleware('permission:notifications/delete', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexNotificationRequest $request)
    {
        $query     = Notification::search($request->search)
        ->query(function($query) use ($request){
            $query->with('group');
            if($request->search){
                $query->orWhereRelation('group','name', 'like', '%' . $request->search . '%');
                $status = NotificationsStatusEnum::getCaseFromEnumData($request->search);
            $type = NotificationsTypeEnum::getCaseFromEnumData($request->search);
                if($status)
                $query->orWhere('status', 'like', '%' . $status . '%');
            if($type)
                $query->orWhere('type', 'like', '%' . $type . '%');
            }
            $query = $this->orderBy($query, $request, 'notifications');
        });
        return NotificationResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    public function clients(NotificationClientsRequest $request)
    {
        $query = Client::search($request->search);
        return NotificationClientsResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreNotificationRequest $request)
    {
        $request['status']   = NotificationsStatusEnum::Scheduled->value;
        $notification = Notification::create($request->all());
        return self::jsonResponse('success', $notification);
    }

    /**
     * Display the specified resource.
     */
    public function show(Notification $notification)
    {
        $notification->load('group');
        return self::jsonResponse('success', $notification);
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateNotificationRequest $request, Notification $notification)
    {
        $notification->update($request->all());
        return self::jsonResponse('success', $notification->refresh());
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Notification $notification)
    {
        $notification->delete();
        return self::jsonResponse('success');
    }

    /**
     * @return false|string
     */
    public function getGroups()
    {
        $groups = Group::select('id', 'name')->get()->toArray();
        return self::jsonResponse('success', $groups);
    }


}
