<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ClientTicketMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    /**
     * Create a new $server instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function build()
    {
        return $this->subject($this->data['subject'])
            ->markdown('mails.client_ticket')
            ->with([
                'ticket_id' => $this->data['ticket_id'],
                'subject' => $this->data['subject'],
                'email' => $this->data['email'],
                'content' => $this->data['body']
            ]);
    }
}
