import axios from "axios";
import BaseService from "./base-service";
import authHeader from "./auth-header";

class ReportsService extends BaseService {
    routPath = '/reports';

    constructor() {
        super();
    }

    getServers(params, showLoader) {
        const queryParams = new URLSearchParams({
            page: params.page,
            limit: params.size,
            search: params.search,
            sort: params.sort,
            ...params
        });
        return axios.get(`${this.routPath}/servers?${queryParams}`
            , { headers: authHeader(), showLoader }
        );
    }

    getServersStatistics(showLoader) {
        return axios.get(this.routPath + '/servers/statistics'
            , { headers: authHeader(), showLoader }
        );
    }

    getAlerts(params, showLoader) {
        const queryParams = new URLSearchParams({
            page: params.page,
            limit: params.size,
            search: params.search,
            sort: params.sort,
            ...params
        });
        return axios.get(`${this.routPath}/alerts?${queryParams}`
            , { headers: authHeader(), showLoader }
        );
    }
}

export default new ReportsService();
