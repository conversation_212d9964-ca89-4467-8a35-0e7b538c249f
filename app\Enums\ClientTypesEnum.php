<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum ClientTypesEnum: string
{
    use EnumTrait;
    
    case FREE = 'free';
    case GUEST = 'guest';
    case PREMIUM = 'premium';

    
    const ENUM_DATA = [
        self::FREE->value => [
            'title_en' => 'Free',
            'title_ar' => 'مجاني',
            'value' => 'free',
        ],
        self::GUEST->value => [
            'title_en' => 'Guest',
            'title_ar' => 'زائر',
            'value' => 'guest',
        ],
        self::PREMIUM->value => [
            'title_en' => 'Premium',
            'title_ar' => 'مميز',
            'value' => 'premium',
        ],
    ];


}

