<?php

namespace App\Http\Controllers\MainControllers;

use App\Enums\TicketStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\TicketRequests\IndexTicketRequest;
use App\Http\Requests\TicketRequests\UpdateTicketRequest;
use App\Http\Resources\Ticket\TicketResource;

use App\Models\Ticket;
use Carbon\Carbon;


class TicketController extends Controller
{
    function __construct()
    {
        $this->middleware('permission:tickets', ['only' => ['index']]);
        $this->middleware('permission:tickets/details', ['only' => ['show']]);
        $this->middleware('permission:tickets/handle', ['only' => ['update']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexTicketRequest $request)
    {
        $query = Ticket::search($request->search)
        ->query(function($query) use ($request){
            $query = $query->with('client');
            if ($request->search){
                $query = $query->orWhereHas('client', function ($subquery) use ($request) {
                    $subquery->where('username', 'like', '%' . $request->search . '%');
                });
                $status = TicketStatusEnum::getCaseFromEnumData($request->search);
                if($status)
                $query->orWhere('status', 'like', '%' . $status . '%');
            }
            $query = $this->orderBy($query, $request, 'tickets');
        });
        return TicketResource::collection($query->paginate($request->limit, 'page', $request->offset));
    }

    /**
     * @param UpdateTicketRequest $request
     * @param Ticket $ticket
     * @return false|string
     */
    public function update(UpdateTicketRequest $request, Ticket $ticket)
    {
        $request['notes_at'] = Carbon::now()->toDateTimeString();
        $request['status']   = TicketStatusEnum::Handled->value;
        $ticket->update($request->all());
        return self::jsonResponse('success', $ticket->refresh());
    }

    /**
     * Display the specified resource.
     */
    public function show(Ticket $ticket)
    {
        $ticket->load('client');
        return self::jsonResponse('success', TicketResource::make($ticket));
    }

}
