export default function sessionsTableItems(t, deleteItem, redirect) {

    const cols = [
        { header: 'name', field: 'sessions.name', cell: (item) => item.name },
        { header: 'token', field: 'sessions.token', cell: (item) => item.token },
        { header: 'secret_key', field: 'sessions.secret_key', cell: (item) => item.secret_key },
        { header: 'locations.name.country_id', field: 'sessions.country', cell: (item) => item?.country?.name },
        { header: 'last_used_at', field: 'sessions.last_used_at', cell: (item) => item.last_used_at_formatted },
        { header: 'device_id', field: 'sessions.device_id', cell: (item) => item.device_id },
        { header: 'device_os', field: 'sessions.device_os', cell: (item) => item.device_os },

    ];

    const actions = [];

    return {
        cols,
        actions
    }
}
