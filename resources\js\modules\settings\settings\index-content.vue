<template>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData?.name_ar ?? $t(router.currentRoute.value.meta.breadcrumb)"
            :reset="parent ? false : true"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container>
        <h3>{{ $t('settings.contents') }}</h3>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <t-data-table
                :rows="tableData"
                :pagination="pagination"
                :query="query"
                :queryType="'index-contentloadContentData'"
                :loading="isLoading"
                :userPermissions="userPermissions"
                :cols="contentCols"
                :actions="contentActions"
                @loadData="loadContentData"
            >
            </t-data-table>
        </div>
    </v-container>
</template>

<script setup>

import TDataTable from "@/shared/components/t-data-table.vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import useSettings from "@/modules/settings/composables/settings";


const {
    parent,
    tableData,
    pagination,
    query,
    isLoading,
    updateModal,
    storeModal,
    cancel,
    itemData,
    contentCols,
    contentActions,
    updateModalItem,
    loadParentData,
    loadData,
    router,
    userPermissions,
    addressCols,
    addressActions,
    valid,
    validation,
    form,
    service,
    setting,
    getItem,
    loadContentData,
} = useSettings()


</script>
