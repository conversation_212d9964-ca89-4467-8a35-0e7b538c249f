const TicketIndex = () => import("@/modules/tickets-management/tickets/index.vue");
const TicketUpdate = () => import("@/modules/tickets-management/tickets/update.vue");
const TicketDetail = () => import("@/modules/tickets-management/tickets/details.vue");

const TicketRoutes = [
    {
        path: '/tickets/:id?',
        name: 'tickets',
        component: TicketIndex,
        props: true,
        meta: {
            breadcrumb: 'tickets.tickets'
        }
    },
    {
        path: "/tickets/:id/update",
        name: 'tickets/handle',
        component: TicketUpdate,
        props: true
    },
    {
        path: "/tickets/:id/details",
        name: 'tickets/details',
        component: TicketDetail,
        props: true,
        meta: {
            breadcrumb: 'tickets.details'
        }
    }
];

export default TicketRoutes;
