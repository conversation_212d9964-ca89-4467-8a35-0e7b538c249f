<?php

namespace App\Http\Requests\GroupRequests;

use App\Http\Requests\BaseRequest;

class DetachClientRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'client_id' => ['required', 'exists_ignore_deleted:clients,id'],
        ];
    }
}
