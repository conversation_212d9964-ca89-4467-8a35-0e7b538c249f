import { ref } from "vue";

export default {
    install: (app, options) => {
        const mainLoader = ref(0);
        const modalLoader = ref(0);
        app.provide('mainLoader', mainLoader);
        app.provide('modalLoader', modalLoader);
        
        options.axios.interceptors.request.use(
            config => {
                if (config.showLoader) {
                    if(config.showLoader === 'modalLoader')
                    modalLoader.value++;
                    else
                    mainLoader.value++;
                }

                return config;
            },
            error => {
                if (error.config.showLoader) {
                    if(error.config.showLoader === 'modalLoader')
                    modalLoader.value--;
                    else
                    mainLoader.value--;
                }
                return Promise.reject(error);
            }
        );
        options.axios.interceptors.response.use(
            response => {
                if (response.config.showLoader) {
                    if(response.config.showLoader === 'modalLoader')
                    modalLoader.value--;
                    else
                    mainLoader.value--;
                }

                return response;
            },
            error => {
                let response = error.response;

                if (response.config.showLoader) {
                    if(response.config.showLoader === 'modalLoader')
                    modalLoader.value--;
                    else
                    mainLoader.value--;
                }

                return Promise.reject(error);
            }
        )
    }
}