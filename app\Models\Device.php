<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\Scout\Searchable;

class Device extends Model
{
    use Searchable;
    protected $guarded = [];
    protected $appends = ['last_connection_at_formatted'];

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {

        return [
            'device_id' => $this->device_id,
            'device_os' => $this->device_os,
            'device_os_version' => $this->device_os_version,
            'app_version' => $this->app_version,
            'fcm_token' => $this->fcm_token,
            'last_connection_at' => $this->last_connection_at,

        ];
    }

    
    public function getLastConnectionAtFormattedAttribute()
    {
        return $this->last_connection_at?->format('Y-m-d h:i:s A');
    }

}
