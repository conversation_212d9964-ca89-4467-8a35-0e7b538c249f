<?php

namespace App\Models\Sanctum;

use App\Models\Location;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken as SanctumPersonalAccessToken;
use <PERSON><PERSON>\Scout\Searchable;

class PersonalAccessToken extends SanctumPersonalAccessToken
{
    use Searchable;

    protected $fillable = [
        'tokenable_id',
        'tokenable_type',
        'token',
        'last_used_at',
        'secret_key',
        'expires_at',
        'country_id',
        'IP',
        'device_id',
        'device_os',
        'name',
        'abilities',
    ];

    protected $appends = ['last_used_at_formatted'];

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'last_used_at' => '',
            'device_id' => '',
            'device_os' => '',
            'token' => '',
            'secret_key' => '',
            'name' => '',
        ];
    }

    
    public function getLastUsedAtFormattedAttribute()
    {
        return $this->last_used_at?->format('Y-m-d h:i:s A');
    }

    public function country()
    {
        return $this->belongsTo(Location::class, 'country_id');
    }
}