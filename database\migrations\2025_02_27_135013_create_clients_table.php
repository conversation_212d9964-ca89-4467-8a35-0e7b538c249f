<?php

use App\Enums\ClientStatusEnum;
use App\Enums\ClientTypesEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    use \App\Traits\LogColumns;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('username', 50)->nullable();
            $table->string('email', 50)->nullable();
            $table->string('password')->nullable();
            $table->enum('type', ClientTypesEnum::getValues())->default(ClientTypesEnum::GUEST->value);
            $table->enum('status', ClientStatusEnum::getValues())->default(ClientStatusEnum::ACTIVE->value);
            $table->string('verification_code', 6)->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamp('registered_at')->nullable();
            $this->LogColumns($table, 1);

            $table->unique(['email', 'deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};

