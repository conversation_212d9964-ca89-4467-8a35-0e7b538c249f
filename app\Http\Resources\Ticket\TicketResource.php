<?php

namespace App\Http\Resources\Ticket;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TicketResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                => $this->id,
            'user_name'         => $this->client->username ?? null,
            'email'             => $this->email ?? null,
            'subject'           => $this->subject,
            'content'           => $this->content,
            'status'            => $this->status,
            'submitted_at'      => $this->created_at ? Carbon::parse($this->created_at)->toDateTimeString(): null,
            'note'              => $this->note,
            'notes_at'          => $this->notes_at ? Carbon::parse($this->notes_at)->toDateTimeString() : null,
        ];
    }
}
