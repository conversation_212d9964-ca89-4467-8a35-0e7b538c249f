<?php

namespace App\Services\MobileServices;

use App\Enums\EmailReportStatusEnum;
use App\Helpers\MailHelper;
use App\Http\Requests\MobileRequests\ServersToConnectRequest;
use App\Http\Requests\MobileRequests\TicketRequests\StoreTicketRequest;
use App\Jobs\ReportTicketJob;
use App\Mail\ClientTicketMail;
use App\Models\Client;
use App\Models\EmailLog;
use App\Models\Location;
use App\Models\Port;
use App\Models\Protocol;
use App\Models\Server;
use App\Models\Ticket;
use Exception;

class TicketService
{
    public function store(StoreTicketRequest $request)
    {
        $input = $request->validated();

        $ticket = Ticket::create($input);

        ReportTicketJob::dispatch($ticket);

        return $ticket;
    }
}
