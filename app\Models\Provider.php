<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Relations\HasMany;
use <PERSON><PERSON>\Scout\Searchable;
use App\Models\Server;

class Provider extends BaseModel
{
    use  Searchable;

    protected $table = 'providers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'contact_number',
        'email',
        'website',
        'admin_url',
        'note',
    ];


    public static function relations(){
        return [
            'servers'
        ];
    }

    public function hasRelations(): bool
    {
        $this->loadCount($this->relations());
        foreach ($this->relations() as $key => $value) {
            if($this[$value . '_count'])
            return true;
        }

        return false;
    }


    /**
     * @return HasMany
     */
    public function servers(): HasMany
    {
        return $this->hasMany(Server::class);
    }

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'name' => '',
            'contact_number' => '',
            'email' => '',
            'website' => '',
            'admin_url' => '',
            'note' => '',
        ];
    }
}
