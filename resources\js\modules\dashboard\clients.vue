<template>
        <v-btn @click="reloadData" icon="mdi-refresh" class="float-end"></v-btn>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
            :reset="true"
        >
        </t-breadcrumbs>


    </v-container>

    <v-container>
        <v-row>
            <!-- Total Clients Card -->
            <v-col cols="12" md="4">
                <t-statistic-card
                    :title="$t('statistics.total_users')"
                    icon="account-group"
                    :value="statistics.total_users"
                    :loading="isLoading"
                />
            </v-col>

            <!-- Free Clients Card -->
            <v-col cols="12" md="4">
                <t-statistic-card
                    :title="$t('statistics.free_users')"
                    icon="account"
                    :value="statistics.free_users"
                    :loading="isLoading"
                />
            </v-col>

            <!-- Paid Clients Card -->
            <v-col cols="12" md="4">
                <t-statistic-card
                    :title="$t('statistics.paid_users')"
                    icon="account-plus"
                    :value="statistics.paid_users"
                    :loading="isLoading"
                />
            </v-col>
        </v-row>

        <v-row>
            <!-- Logged in clients section -->
            <v-col cols="12" md="6">
                <v-card
                    class="mb-4"
                    elevation="4"
                    variant="elevated"
                    color="white"
                    :loading="isLoading"
                >
                    <v-card-title>
                        <h6>
                            <v-icon>mdi mdi-account-check</v-icon>
                            {{$t('statistics.logged_in_users')}}
                        </h6>
                    </v-card-title>
                    <v-table class="text-black">
                        <thead>
                            <tr>
                                <th class="font-weight-bold text-start">
                                    {{ $t("statistics.period") }}
                                </th>
                                <th class="font-weight-bold text-start">
                                    {{ $t("statistics.users") }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{ $t("statistics.today") }}</td>
                                <td class="text-start">
                                    {{ statistics.logged_in_users.today }}
                                </td>
                            </tr>
                            <tr>
                                <td>{{ $t("statistics.last_7_days") }}</td>
                                <td class="text-start">
                                    {{
                                        statistics.logged_in_users
                                            .last_7_days
                                    }}
                                </td>
                            </tr>
                            <tr>
                                <td>{{ $t("statistics.last_30_days") }}</td>
                                <td class="text-start">
                                    {{
                                        statistics.logged_in_users
                                            .last_30_days
                                    }}
                                </td>
                            </tr>
                        </tbody>
                    </v-table>
                </v-card>
            </v-col>

            <!-- New clients and conversion rate -->
            <v-col cols="12" md="6">
                <v-card
                    class="mb-4"
                    elevation="4"
                    variant="elevated"
                    color="white"
                    :loading="isLoading"
                >
                    <v-card-title>
                        <h6>
                            <v-icon>mdi mdi-account-plus</v-icon>
                            {{$t('statistics.new_users_and_conversion')}}
                        </h6>
                    </v-card-title>
                    <v-table class="text-black">
                        <thead>
                            <tr>
                                <th class="font-weight-bold text-start">
                                    {{ $t("statistics.metric") }}
                                </th>
                                <th class="font-weight-bold text-start">
                                    {{ $t("statistics.value") }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    {{ $t("statistics.new_users_this_month") }}
                                </td>
                                <td class="text-start">
                                    {{ statistics.new_users }}
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    {{
                                        $t("statistics.free_to_paid_conversion")
                                    }}
                                </td>
                                <td class="text-start">
                                    {{ statistics.conversion_rate }}%
                                </td>
                            </tr>
                        </tbody>
                    </v-table>
                </v-card>
            </v-col>

            <!-- Client distribution by country -->
            <v-col cols="12" md="6">
                <v-card
                    class="mb-4"
                    elevation="4"
                    variant="elevated"
                    color="white"
                    :loading="isLoading"
                >
                    <v-card-title>
                        <h6>
                            <v-icon>mdi mdi-earth</v-icon>
                            {{$t('statistics.user_distribution_by_country')}}
                        </h6>
                    </v-card-title>

                    <v-table class="statistics-table">
                        <thead>
                            <tr>
                                <th class="font-weight-bold text-start">
                                    {{ $t("statistics.country") }}
                                </th>
                                <th class="font-weight-bold text-start">
                                    {{ $t("statistics.users") }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                            class="text-start"
                                v-for="(
                                    item, index
                                ) in statistics.user_distribution"
                                :key="index"
                            >
                                <td>{{ item.country }}</td>
                                <td class="text-start">
                                    {{ item.user_count }}
                                </td>
                            </tr>
                            <tr
                                v-if="statistics.user_distribution.length === 0"
                            >
                                <td colspan="2" class="text-center">
                                    {{ $t("statistics.no_data") }}
                                </td>
                            </tr>
                        </tbody>
                    </v-table>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script setup>
import { onMounted } from "vue";
import useClients from "./composables/clients.js";
import useShared from "@/helpers/shared.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import TStatisticCard from "@/shared/components/t-statistic-card.vue";

const { statistics, isLoading, fetchStatistics, reloadData } = useClients();

const { router } = useShared();

onMounted(() => {
    fetchStatistics();
});
</script>

<style lang="css" scoped>

</style>
