<?php

namespace App\Models;

use App\Traits\TranslatedAttributes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Lara<PERSON>\Scout\Searchable;

class Location extends BaseModel
{
    use  Searchable;

    protected $table = 'locations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'parent_id',
        'level',
        'code',
        'name',
        'auto_protocol_id',
        'auto_location_id',
        'free_servers_count',
        'premium_servers_count',
    ];

    /**
     * @return BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'parent_id');
    }

    /**
     * @return HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(Location::class, 'parent_id');
    }

    /**
     * @return BelongsTo
     */
    public function autoLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'auto_location_id');
    }

    /**
     * @return BelongsTo
     */
    public function autoProtocol(): BelongsTo
    {
        return $this->belongsTo(Protocol::class, 'auto_protocol_id');
    }

    /**
     * @return mixed
     */
    public function grandParent()
    {
        return $this->parent?->parent;
    }

    public function servers()
    {
        return $this->hasManyThrough(Server::class, Location::class, 'parent_id', 'location_id');
    }

    /**
     * @return bool
     */
    public function hasRelations(): bool
    {
        if (sizeof($this->children) > 0)
            return true;
        return false;
    }

    /**
     * Get the display count for free servers
     *
     * @return int
     */
    public function getFreeServersCount(): int
    {
        return $this->free_servers_count ?? 0;
    }

    /**
     * Get the display count for premium servers
     *
     * @return int
     */
    public function getPremiumServersCount(): int
    {
        return $this->premium_servers_count ?? 0;
    }

    /**
     * Get the total display server count (free + premium)
     *
     * @return int
     */
    public function getTotalServersCount(): int
    {
        return $this->getFreeServersCount() + $this->getPremiumServersCount();
    }

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'name' => '',
            'code' => '',
        ];
    }
}
