const ProviderIndex = () => import("@/modules/providers-management/providers/index.vue");
const ProviderCreate = () => import("@/modules/providers-management/providers/create.vue");
const ProviderUpdate = () => import("@/modules/providers-management/providers/update.vue");

const ProviderRoutes = [
    {
        path: '/providers/:id?',
        name: 'providers',
        component: ProviderIndex,
        props: true,
        meta: {
            breadcrumb: 'providers.providers'
        }
    },
    {
        path: '/providers/create',
        name: 'providers/create',
        component: ProviderCreate
    },
    {
        path: "/providers/:id/update",
        name: 'providers/update',
        component: ProviderUpdate,
        props: true
    },
];

export default ProviderRoutes;
