version: '3.9'

networks:
  default:
    name: dev05
    external: true

services:
  system:
    container_name: $CI_PROJECT_PATH_SLUG-$CI_COMMIT_BRANCH
    build:
      context: ./
      dockerfile: ./Dockerfile
    labels:
      io.portainer.accesscontrol.teams: captinvpn,System Admins
    env_file:
      - $DEV_05_ENV
    restart: unless-stopped
    volumes:
      - /opt/project-data/captinvpn:/www/storage
