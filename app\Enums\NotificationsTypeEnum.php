<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum NotificationsTypeEnum: string
{
    use EnumTrait;

    case One       = 'one';
    case Group     = 'group';
    case Free      = 'free';
    case Premium   = 'premium';
    case All       = 'all';


    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }

    const ENUM_DATA = [
        self::One->value => [
            'title_en' => 'Client',
            'title_ar' => 'مستخدم',
            'value' => 'one',
        ],
        self::Group->value => [
            'title_en' => 'Group',
            'title_ar' => 'مجموعة',
            'value' => 'group',
        ],
        self::Free->value => [
            'title_en' => 'Free',
            'title_ar' => 'مجاني',
            'value' => 'free',
        ],
        self::Premium->value => [
            'title_en' => 'Premium',
            'title_ar' => 'مميز',
            'value' => 'premium',
        ],
        self::All->value => [
            'title_en' => 'All',
            'title_ar' => 'الكل',
            'value' => 'all',
        ],
    ];
}
