<template>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData?.subject ?? $t(router.currentRoute.value.meta.breadcrumb)"
            :reset="parent ? false : true"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container>
        <h3>{{ $t('packages.packages') }}</h3>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-btn
            v-if="userPermissions.includes('packages/create')"
            :to="{ name: 'packages/create' }" :class="'float-'+$t('right') + ' colored-btn'" >
            <span class="px-2">{{ $t('packages.add') }}</span>
            <img class="crud-icon" src="@/assets/icons/ic_add_2.svg">
        </v-btn>
        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <t-data-table
                :rows="tableData"
                :pagination="pagination"
                :query="query"
                :queryType="'LoadData'"
                :loading="isLoading"
                :userPermissions="userPermissions"
                :cols="PackageCols"
                :actions="PackageActions"
                @loadData="loadData"
            >
            </t-data-table>
        </div>
    </v-container>
</template>

<script setup>

import TDataTable from "@/shared/components/t-data-table.vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import usePackages from "@/modules/packages-management/composables/packages";
import {onMounted} from "vue";

const {
    parent,
    tableData,
    pagination,
    query,
    isLoading,
    updateModal,
    storeModal,
    cancel,
    itemData,
    showStoreModal,
    storeModalItem,
    updateModalItem,
    loadParentData,
    loadData,
    router,
    userPermissions,
    valid,
    validation,
    form,
    getItem,
    PackageActions,
    PackageCols,
} = usePackages()


</script>
