<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum ExpenseTypesEnum: string
{
    use EnumTrait;

    case GENERAL = 'general';
    case SERVER = 'server';

    const ENUM_DATA = [
        self::GENERAL->value => [
            'title_en' => 'General',
            'title_ar' => 'عام',
            'value' => 'general',
        ],
        self::SERVER->value => [
            'title_en' => 'Server',
            'title_ar' => 'خادم',
            'value' => 'server',
        ],
    ];
}
