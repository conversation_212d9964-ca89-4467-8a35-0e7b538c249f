<?php

namespace App\Http\Requests\ServerRequests;

use App\Enums\ServerInternalStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdateServerRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'integer', 'exists_ignore_deleted:servers'],
            'location_id' => ['required', 'integer', Rule::exists('locations', 'id')->whereNotNull('parent_id')->whereNull('deleted_at')],
            'provider_id' => ['required', 'integer', 'exists_ignore_deleted:providers,id'],
            'name' => ['required', 'string', 'min:3', 'max:50', 'unique_ignore_deleted:servers,name,' . $this->id],
            'ip' => ['required', 'string', 'ip', 'max:50'],
            'socket_port' => ['required', 'string', 'max:4'],
            'cost' => ['required', 'numeric', 'decimal:0,2', 'between:0,9999.99'],
            'is_free' => ['required', 'boolean'],
            'internal_status' => ['required', 'string', Rule::in(ServerInternalStatus::asArray())],
            'ram' => ['required', 'numeric'],
            'cpu' => ['required', 'numeric'],
            'disk' => ['required', 'numeric'],
            'ram_threshold' => ['nullable', 'numeric', 'decimal:0,2', 'between:0,100'],
            'cpu_threshold' => ['nullable', 'numeric', 'decimal:0,2', 'between:0,100'],
            'disk_threshold' => ['nullable', 'numeric', 'decimal:0,2', 'between:0,100']
        ];
    }
}
