import GroupIndex from "@/modules/groups-management/groups/index.vue";
import GroupCreate from "@/modules/groups-management/groups/create.vue";
import GroupUpdate from "@/modules/groups-management/groups/update.vue";
import GroupDetails from "@/modules/groups-management/groups/details.vue";

const GroupsManagementRoutes = [
    {
        path: '/groups',
        name: 'groups',
        component: GroupIndex,
        meta: {
            breadcrumb: 'groups.groups'
        }
    },
    {
        path: '/groups/create',
        name: 'groups/create',
        component: GroupCreate,
        props: true,
        meta: {
            breadcrumb: 'groups.add'
        }
    },
    {
        path: "/groups/:id/update",
        name: 'groups/update',
        component: GroupUpdate,
        props: true,
        meta: {
            breadcrumb: 'groups.edit'
        }
    },
    {
        path: "/groups/:id/details",
        name: 'groups/details',
        component: GroupDetails,
        props: true,
        meta: {
            breadcrumb: 'groups.details'
        }
    },
]

export default GroupsManagementRoutes;