<?php

namespace App\Http\Controllers\MobileControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\MobileRequests\ServersToConnectRequest;
use App\Services\MobileServices\PortService;
use Illuminate\Http\Request;

class PortController extends Controller
{
    protected $service;

    public function __construct()
    {
        $this->service = new PortService();
    }

    public function serversToConnect(ServersToConnectRequest $request)
    {
        $client = auth()->user();
        $servers = $this->service->getTopServers($request, $client, 5);

        if (!count($servers)) {
            // throw exeception
        }

        return self::MobileResponse('success', $servers);
    }
}
