<?php

namespace App\Services;

use App\Enums\PortStatusEnum;
use App\Enums\ServerHealthStatus;
use App\Http\Requests\VpnRequests\UpdateConnectionsCountRequest;
use App\Http\Requests\VpnRequests\VpnServerRequest;
use App\Models\Server;
use Illuminate\Support\Facades\DB;

class VpnServerService
{
    public function updateResourcesStatus(VpnServerRequest $request)
    {
        DB::beginTransaction();
        try {
            $serverId = auth()->user()->id;
            $server = Server::where('id', $serverId)->first();

            /**
             * Update the server resources consumption
             * If the threshold greater than the consumption for at least one resource
             * then the server is unhelathy
             */
            $isHealthy = true;
            foreach ($request->validated('resources') as $resource) {
                $serverResource =  $server->resources()->where('resource', $resource['name'])->firstOrFail();
                $serverResource->update([
                    'consumption' => $resource['consumption'],
                ]);
                $isHealthy &= ($serverResource['threshold'] > $resource['consumption']);
            }

            /**
             * Update the server health status and last connection at
             */
            $server->update([
                'health_status' => $isHealthy ?
                    ServerHealthStatus::HEALTHY->value
                    : ServerHealthStatus::NOT->value,
                'last_connection_at' => now()
            ]);

            DB::commit();
            return true;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function updateConnectionsCount(UpdateConnectionsCountRequest $request)
    {
        $serverId = auth()->user()->id;
        $server = Server::where('id', $serverId)->first();
        $currentConnectionsCount = $request->validated('connections_count');

        /**
         * Get the requested port
         */
        $port = $server->ports()
            ->where('port', $request->validated('port'))
            ->first();

        /**
         * If the current connection count is greater than the port connections threshold
         * then the port should be disabled
         */
        $shouldDisable = false;
        if ($currentConnectionsCount >= $port['connections_threshold'])
            $shouldDisable = true;

        /**
         * Update the port current connections and status
         */
        $port->update([
            'current_connections_count' => $request->validated('connections_count'),
            'status' => ($shouldDisable ? PortStatusEnum::DISABLED->value : PortStatusEnum::ENABLED->value)
        ]);
        return true;
    }
}
