<template>
    <v-container>
        <v-card width="90%" class="pa-5 mx-auto" color="white" outlined>
            <v-card-title class="headline black" primary-title>
                <h3>
                    {{ $t('providers.edit') }}
                </h3>
            </v-card-title>
            <v-card-text class="pa-5">
                <v-form
                    v-model="valid"
                    v-on:submit.prevent="emit('update', {...updateForm}, 'providers', true)"
                >
                    <v-row class="mt-n2">
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="updateForm.name"
                                :label="$t('providers.name')"
                                variant="outlined"
                                :rules="validation.name"
                                class="required"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="updateForm.contact_number"
                                :label="$t('providers.contact_number')"
                                variant="outlined"
                                :rules="validation.contact_number"
                            >
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="updateForm.email"
                                :label="$t('providers.email')"
                                variant="outlined"
                                :rules="validation.email"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="updateForm.website"
                                :label="$t('providers.website')"
                                variant="outlined"
                                :rules="validation.website"
                            >
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="updateForm.admin_url"
                                :label="$t('providers.admin_url')"
                                variant="outlined"
                                :rules="validation.admin_url"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" lg="6">
                            <v-textarea
                                v-model="updateForm.note"
                                :label="$t('providers.note')"
                                variant="outlined"
                                :rules="validation.note"
                                rows="3"
                            >
                            </v-textarea>
                        </v-col>
                    </v-row>
                    <v-btn
                        @click="emit('cancel')"
                        :class="'float-' + $t('right')"
                        class="colored-btn-cancel"
                    >
                        {{ $t("cancel") }}
                    </v-btn>
                    <v-btn
                        :class="'float-' + $t('right') + ' colored-btn'"
                        type="submit"
                    >
                        <span class="px-2">{{ $t("edit") }}</span>
                    </v-btn>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>
<script setup>
import { ref } from 'vue';

const valid = defineModel("valid", {type: Boolean});
const form = defineModel("form", {type: Object});
const props = defineProps(["validation"]);
const emit = defineEmits(["cancel", "update"]);

const updateForm = ref({...form.value});
</script>
