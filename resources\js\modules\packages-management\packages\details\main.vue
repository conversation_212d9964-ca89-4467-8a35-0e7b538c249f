<template>
    <div>
        <v-row>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("packages.name") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.name }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{$t("packages.duration")}}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.duration }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("packages.price") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.price }}$
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("packages.unit") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ getUnitLabel(itemData.unit) }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("packages.status") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ getStatusLabel(itemData.status) }}
                </b>
            </v-col>

        </v-row>

    </div>
</template>

<script setup>
import usePackages from "@/modules/packages-management/composables/packages";
import {onMounted, computed} from "vue";
import PackageStatusEnum from "@/enums/package-status-enum";
import PackageUnitEnum from "@/enums/package-unit-enum";


const { t } = usePackages();

const props = defineProps({
    itemData: {
        required: true,
        type: Object
    }
});

const getStatusLabel = (status) => {
    const { enumData } = PackageStatusEnum();
    return enumData[status]?.[`title_${t("locale.lang")}`] || status;
};

const getUnitLabel = (unit) => {
    const { enumData } = PackageUnitEnum();
    return enumData[unit]?.[`title_${t("locale.lang")}`] || unit;
};

onMounted(() => {
});
</script>



