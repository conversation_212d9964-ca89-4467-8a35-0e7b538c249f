<?php

namespace App\Mail;

use App\Enums\SettingEnum;
use App\Models\Setting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class VerificationCodeMail extends Mailable
{
    use Queueable, SerializesModels;

    public $verificationCode;
    public $fromEmail;
    
    /**
     * Create a new message instance.
     */
    public function __construct($verificationCode)
    {
        $this->verificationCode = $verificationCode;
        $this->fromEmail = Setting::where('type', SettingEnum::Email)->firstOrFail();
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Verification Code Mail',
        );
    }

    public function build()
    {
        return $this->subject('Verify Your Email Address')
            ->from($this->fromEmail->value)
            ->view('mails.verification_code');
    }
}
