import { reactive} from 'vue'
import ticketsService from "@/services/tickets-management-services/tickets-service.js";
import useShared from "@/helpers/shared.js";
import ticketTableItems from "../models/ticket-table-items";
import ticketStatusEnum from "@/enums/ticket-status-enum";

export default function useTickets() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        cancel,
        getItem,
        loadData,
        storeItem,
        updateItem,
        deleteItem,
        updateModalItem,
        updateModal,
        showUpdateModal,
        parent,
        router,
        userPermissions,
        t,
        redirect
    } = useShared()

    service.value = ticketsService;

    const {
        cases: ticketStatusCases,
        enumData: ticketStatusEnumData
    } = ticketStatusEnum();

    const {
        cols: ticketCols,
        actions: ticketActions
    } = ticketTableItems(t, redirect,showUpdateModal);


    const form = reactive({
        'note': null,
    });

    const validation = {
        note: [
            validationRules.required
        ],
    }





    return {
        itemData,
        tableData,
        pagination,
        query,
        isLoading,
        form,
        validation,
        valid,
        cancel,
        loadData,
        getItem,
        storeItem,
        updateItem,
        deleteItem,
        updateModalItem,
        updateModal,
        parent,
        router,
        userPermissions,
        ticketActions,
        ticketCols,
        ticketStatusCases,
        ticketStatusEnumData,
        showUpdateModal
    }
}
