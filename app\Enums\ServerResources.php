<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum ServerResources: string
{
    /**
     * Example of enum
     */
    use EnumTrait;

    case RAM = 'ram';
    case CPU = 'cpu';
    case DISK = 'disk';

    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }

    public function threshold(): string
    {
        return match($this)
        {
            self::RAM => 80,
            self::CPU => 80,
            self::DISK => 80
        };
    }

    public function code(): string
    {
        return match($this)
        {
            self::RAM => 'serv_ram_t',
            self::CPU => 'serv_cpu_t',
            self::DISK => 'serv_dis_t'
        };
    }

    const ENUM_DATA = [
        self::RAM->value => [
            'title_en' => 'RAM',
            'title_ar' => 'RAM',
            'settings_attr' => 'serv_ram_t',
            'value' => 'ram',
        ],
        self::CPU->value => [
            'title_en' => 'CPU',
            'title_ar' => 'CPU',
            'settings_attr' => 'serv_cpu_t',
            'value' => 'cpu',
        ],
        self::DISK->value => [
            'title_en' => 'Disk',
            'title_ar' => 'Disk',
            'settings_attr' => 'serv_dis_t',
            'value' => 'disk',
        ]
    ];
}
