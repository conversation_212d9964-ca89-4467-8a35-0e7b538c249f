
export default function providerTableItems(t, redirect, showUpdateModal, deleteItem) {

    const cols = [
        { header: 'name', field: 'providers.name', cell: (item) => item.name },
        { header: 'contact_number', field: 'providers.contact_number', cell: (item) => item.contact_number },
        { header: 'email', field: 'providers.email', cell: (item) => item.email },
        { header: 'website', field: 'providers.website', cell: (item) => item.website },
        { header: 'admin_url', field: 'providers.admin_url', cell: (item) => item.admin_url },
        { header: 'note', field: 'providers.note', cell: (item) => item.note },
    ];

    const actions = [
        {
            header: 'update',
            perm: 'providers/update',
            icon: "mdi-pencil",
            action: (item) => showUpdateModal(item)
        },
        {
            header: 'delete',
            perm: 'providers/delete',
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id)
        }
    ];

    return {
        cols,
        actions
    }
}
