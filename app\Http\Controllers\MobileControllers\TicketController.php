<?php

namespace App\Http\Controllers\MobileControllers;

use App\Enums\EmailReportStatusEnum;
use App\Helpers\MailHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\MobileRequests\TicketRequests\StoreTicketRequest;
use App\Mail\ServerDownMail;
use App\Models\EmailLog;
use App\Models\Ticket;
use App\Services\MobileServices\TicketService;
use Exception;
use Illuminate\Http\Request;

class TicketController extends Controller
{
    protected $service;

    public function __construct() {
        $this->service = new TicketService();
    }

    /**
     */

    public function store(StoreTicketRequest $request)
    {
        $this->service->store($request);

        return self::MobileResponse('success', []);
    }


}
