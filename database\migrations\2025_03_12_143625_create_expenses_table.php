<?php

use App\Enums\ExpenseTypesEnum;
use App\Traits\LogColumns;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    use LogColumns;
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('server_id')->nullable()->constrained('servers');
            $table->decimal('amount', 5, 2);
            $table->text('description');
            $table->enum('type', ExpenseTypesEnum::getValues())->default(ExpenseTypesEnum::GENERAL->value);
            $table->date('payment_date');
            $this->LogColumns($table);
            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};
