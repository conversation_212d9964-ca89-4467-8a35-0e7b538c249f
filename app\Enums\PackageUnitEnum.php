<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum PackageUnitEnum: string
{
    use EnumTrait;

    case Days     = 'days';
    case Months   = 'months';
    case Years    = 'years';

    const ENUM_DATA = [
        self::Days->value => [
            'title_en' => 'Days',
            'title_ar' => 'أيام',
            'value' => 'days',
        ],
        self::Months->value => [
            'title_en' => 'Months',
            'title_ar' => 'شهور',
            'value' => 'months',
        ],
        self::Years->value => [
            'title_en' => 'Years',
            'title_ar' => 'سنوات',
            'value' => 'years',
        ],
    ];

    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }
}
