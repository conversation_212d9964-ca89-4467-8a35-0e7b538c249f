<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('personal_access_tokens', function (Blueprint $table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('token', 64)->unique();
            $table->timestamp('last_used_at')->nullable();
            $table->string('secret_key', 255)->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->integer('country_id')->nullable()->constrained('countries');
            $table->string('IP', 20)->nullable();
            $table->string('device_id', 50)->nullable();
            $table->string('device_os', 50)->nullable();
            $table->string('name')->nullable();
            $table->text('abilities')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('personal_access_tokens');
    }
};
