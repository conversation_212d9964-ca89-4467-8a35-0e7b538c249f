<?php

namespace Database\Seeders;

use App\Enums\ServerResources;
use App\Enums\SettingEnum;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $userId = User::first()->id;

        $settings = [
            [
             'name'       => 'Email Address',
             'type'       => SettingEnum::Email ,
             'code'       =>'email' ,
             'is_content' => false,
             'value'      =>'<EMAIL>'
            ],

            [
                'name' => 'Max Connection Time',
                'type' => SettingEnum::Numeric ,
                'is_content' => false,
                'code' =>'conn_limit' ,
                'value' =>'180'
            ],

            [
                'name'  => 'Ads Activation',
                'type'  => SettingEnum::Boolean ,
                'is_content' => false,
                'code'  =>'ads_act' ,
                'value' =>false
            ],

            [
                'name' => 'Upgrade To Premium',
                'type' => SettingEnum::Boolean ,
                'is_content' => false,
                'code' =>'premium',
                'value'=>false
            ],

            [
                'name' => 'Server RAM threshold (%)',
                'type' => SettingEnum::Numeric ,
                'is_content' => false,
                'code' => ServerResources::RAM->code(),
                'value'=> ServerResources::RAM->threshold()
            ],

            [
                'name' => 'Server CPU threshold (%)',
                'type' => SettingEnum::Numeric ,
                'is_content' => false,
                'code' => ServerResources::CPU->code(),
                'value'=> ServerResources::CPU->threshold()
            ],

            [
                'name' => 'Server DISK threshold (%)',
                'type' => SettingEnum::Numeric ,
                'is_content' => false,
                'code' => ServerResources::DISK->code(),
                'value'=> ServerResources::DISK->threshold()
            ],

            [
                'name' => 'Phone1',
                'type' => SettingEnum::Text ,
                'is_content' => false,
                'code' =>'pho1',
                'value'=>'+971-4354-332'
            ],

            [
                'name' => 'facebook',
                'type' => SettingEnum::Link ,
                'is_content' => false,
                'code' =>'acc_fa',
                'value'=>'https://www.facebook.com/CaptainVpn'
            ],


            [
                'name' => 'instagram',
                'type' => SettingEnum::Link ,
                'is_content' => false,
                'code' =>'acc_in',
                'value'=>'https://www.instagram.com/CaptainVpn'
            ],

            [
                'name' => 'X',
                'type' => SettingEnum::Link ,
                'is_content' => false,
                'code' =>'acc_x',
                'value'=>'https://www.x.com/CaptainVpn'
            ],


            [
                'name' => 'Youtube',
                'type' => SettingEnum::Link ,
                'is_content' => false,
                'code' =>'acc_yo',
                'value'=>'https://www.Youtube.com/CaptainVpn'
            ],

            [
                'name' => 'About Us',
                'type' => SettingEnum::Text ,
                'is_content' => true,
                'code' =>'abo_us',
                'value'=>'Captain Vpn is ....'
            ],

            [
                'name' => 'privacy policy',
                'type' => SettingEnum::Text ,
                'is_content' => true,
                'code' =>'pr_po',
                'value'=>'We are committed to maintaining the accuracy, confidentiality, and security of your personally identifiable information ("Personal Information")....'
            ],

            [
                'name' => 'Terms of Service',
                'type' => SettingEnum::Text ,
                'is_content' => true,
                'code' =>'ter_s',
                'value'=>'Acceptance of Terms: By using our service, you agree to these rules...'

            ],

            [
                'name' => 'Banner Ad Activation',
                'type' => SettingEnum::Boolean,
                'is_content' => false,
                'code' => 'ban_ad_act',
                'value' => false
            ],

            [
                'name' => 'Video Ad Activation',
                'type' => SettingEnum::Boolean,
                'is_content' => false,
                'code' => 'vid_ad_act',
                'value' => false
            ],

            [
                'name' => 'Main Load Ad Activation',
                'type' => SettingEnum::Boolean,
                'is_content' => false,
                'code' => 'ml_ad_act',
                'value' => false
            ],

            [
                'name' => 'All Ads Activation',
                'type' => SettingEnum::Boolean,
                'is_content' => false,
                'code' => 'all_ad_act',
                'value' => false
            ],

            [
                'name' => 'Golden Hour Ad Period',
                'type' => SettingEnum::Numeric,
                'is_content' => false,
                'code' => 'gh_ad_per',
                'value' => 60
            ],

            [
                'name' => 'One Golden Hour Activation',
                'type' => SettingEnum::Boolean,
                'is_content' => false,
                'code' => 'one_gh_act',
                'value' => false
            ],

            [
                'name' => 'Protocol Input Name',
                'type' => SettingEnum::Text,
                'is_content' => false,
                'code' => 'prot_name',
                'value' => 'select mode'
            ],

            [
                'name' => 'Server Input Name',
                'type' => SettingEnum::Text,
                'is_content' => false,
                'code' => 'serv_name',
                'value' => 'select country'
            ],

        ];

        foreach ($settings as $setting) {
           Setting::firstOrCreate(
            [
                'type'        => $setting['type'],
                'code'        => $setting['code'],
            ],
            [
                'name'        => $setting['name'],
                'is_content'  => $setting['is_content'],
                'value'       => $setting['value'],
                'created_by'  => $userId,
            ]
        );
        }
    }
}
