<?php

namespace App\Http\Controllers\MobileControllers;

use App\Enums\ClientTypesEnum;
use App\Enums\ServerInternalStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\MobileResources\LocationResource;
use App\Http\Resources\MobileResources\ServerProtocolsResource;
use App\Models\Location;
use App\Services\MobileServices\LocationService;

class ServerController extends Controller
{
    protected $locationService;

    public function __construct()
    {
        $this->locationService = new LocationService();
    }

    /**
     */
    public function countries()
    {
        $countries = $this->locationService->getCountries();
        return self::MobileResponse('success', LocationResource::collection($countries));
    }

    public function protocols(Location $location)
    {
        $location->load(['servers' => function ($server) {
            $server->with('protocols:id,name,version')
                ->enabled()->up();
            if(auth()->user()->client_type !== ClientTypesEnum::PREMIUM->value) {
                $server->free();
            }
        }]);
        $protocols = $location->servers->pluck('protocols')->flatten()->unique('id')->values();
        return self::MobileResponse('success', ServerProtocolsResource::collection($protocols));

    }


}
