import 'vuetify/styles'
import {createVuetify} from 'vuetify';
import * as components from 'vuetify/components';
import * as directives from 'vuetify/directives';
import {aliases, mdi} from 'vuetify/iconsets/mdi'
import '@mdi/font/css/materialdesignicons.css'
import { ar, en } from 'vuetify/locale'

const customTheme = {
    dark: false,
    colors: {
        background: '#F1EFFD',
        surface: '#F1EFFD',
        primary: '#F1EFFD',
        error: '#FA7575',
        info: '#9FD8E7',
        success: '#55E0B2',
        warning: '#FCD17C',

        // Navy Blue base color
        navyBlue: '#1b304d',

        // Navy Blue lightness variations
        'navyBlue-lighten-5': '#e4e7ed',
        'navyBlue-lighten-4': '#bcc3d2',
        'navyBlue-lighten-3': '#919cb4',
        'navyBlue-lighten-2': '#677595',
        'navyBlue-lighten-1': '#465980',
        'navyBlue-darken-1': '#182a44',
        'navyBlue-darken-2': '#14233a',
        'navyBlue-darken-3': '#111d30',
        'navyBlue-darken-4': '#0d1626',

        // Navy Blue accent variations
        'navyBlue-accent-1': '#6b8eff',
        'navyBlue-accent-2': '#3e6aff',
        'navyBlue-accent-3': '#1c52ff',
        'navyBlue-accent-4': '#0040ff',
    },
}

export default createVuetify({
    theme: {
        defaultTheme: 'customTheme',
        themes: {
            customTheme,
        },
    },
    locale: {
        locale: 'ar',
        fallback: 'ar',
        messages: { ar, en },
        rtl: {ar: true},
    },
    components,
    directives,
    icons: {
        defaultSet: 'mdi',
        aliases,
        sets: {
            mdi,
        }
    },
});
