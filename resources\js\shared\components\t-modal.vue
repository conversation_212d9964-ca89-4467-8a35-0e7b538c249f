<template>
 <v-dialog persistent v-model="show" :width="width">
                         <t-modal-loader v-if="modalLoader>0"></t-modal-loader>
     
      <slot></slot>
 </v-dialog>
</template>
<script setup>
import { inject } from 'vue';
import TModalLoader from "./t-loader.vue";

const modalLoader = inject('modalLoader');
const show = defineModel("show", {type: Boolean});
const props = defineProps(["width"]);
</script>
