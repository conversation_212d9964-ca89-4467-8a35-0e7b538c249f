import {reactive, ref} from 'vue'
import ExpenseService from "@/services/expenses-service.js";
import useShared from "@/helpers/shared.js";
import expenseTableItems from '../models/expenses-table-items';
import expenseTypes from "@/enums/expense-types.js";

export default function useExpenses() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        storeItem,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadParentData,
        deleteItem,
        cancel,
        loadData,
        saveItem,
        router,
        userPermissions,
        t,
        errorHandle,
        redirect
    } = useShared()

    service.value = ExpenseService;

    const {
        cols: expenseCols,
        actions: expenseActions
    } = expenseTableItems(t, deleteItem, redirect);



    const form = reactive({
        server_id: null,
        description: '',
        amount: '',
        payment_date: '',
        type: expenseTypes().cases.GENERAL,
    });

    const validation = {
        description: [
            validationRules.required,
        ],
        amount: [
            validationRules.required,
            validationRules.numeric
        ],
        payment_date: [
            validationRules.required
        ],
        type: [
            validationRules.required
        ],
        server_id: [
            validationRules.required
        ],
    }
    const servers = ref([])
    
    const getServers = async () => {
        try {
            const response = await service.value.servers();
            servers.value = response.data.data;
        } catch (error) {
            await errorHandle(error)
        }
    }




    return {
        expenseTypes,
        itemData,
        getServers,
        servers,
        tableData,
        pagination,
        query,
        isLoading,
        loadData,
        validation,
        form,
        valid,
        parent,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        getItem,
        loadParentData,
        storeItem,
        parentDetails,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        expenseCols,
        expenseActions,
        form
    }
}
