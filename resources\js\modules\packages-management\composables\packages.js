import {reactive, ref} from 'vue'
import PackageService from "@/services/package-management-services/package-service.js";
import SubscriptionService from "@/services/subscription-management-services/subscription-service.js";
import useShared from "@/helpers/shared.js";
import PackageTableItems from '../models/package-table-item';
import PackageStatusEnum from "@/enums/package-status-enum";
import PackageUnitEnum from "@/enums/package-unit-enum";
import SubscriptionStatusEnum from "@/enums/subscription-status-enum";

export default function usePackages() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        storeItem,
        loadData,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadParentData,
        deleteItem,
        errorHandle,
        cancel,
        saveItem,
        router,
        userPermissions,
        t,
        cookie,
        redirect,
        createConfirmDialog,
        ConfirmDialog,
        notify
    } = useShared()

    service.value = PackageService;

    const packages = ref();
    const packageStatus = ref([]);
    const unitStatus = ref([]);
    const subscriptionStatus = ref([]);
    const Subscriptions = ref([]);
    const subscriptionPagination = ref({});
    const tabs = ref('subscriptions');
    const SubscriptionsIsLoading = ref(true)


    const getPackageStatus = () => {
        const { enumData } = PackageStatusEnum();
        packageStatus.value = Object.values(enumData);
    };

    const getUnitStatus = () => {
        const { enumData } = PackageUnitEnum();
        unitStatus.value = Object.values(enumData);
    };

    const getSubscriptionStatus = () => {
        const { enumData } = SubscriptionStatusEnum();
        subscriptionStatus.value = Object.values(enumData);
    };

    const disablePackage = async (id) => {
        const dialog = createConfirmDialog(ConfirmDialog);

        dialog.onConfirm(async () => {
            try {
                let response = await service.value.disable(id);
                notify(response.data.message);
            } catch (error) {
                await errorHandle(error)
            }
            await getItem(id, true);
            await loadData();
        });
        await dialog.reveal();
    };

    const disableSubscription = async (id) => {
        const dialog = createConfirmDialog(ConfirmDialog);

        dialog.onConfirm(async () => {
            try {
                let response = await SubscriptionService.disable(id);
                notify(response.data.message);
            } catch (error) {
                await errorHandle(error)
            }
            await getSubscriptions();
        });
        await dialog.reveal();
    };


    const subscriptionQuery = ref({
        search: '',
        page: 1,
        sort:'',
        per_page: 10,
    })


    const {
        cols: PackageCols,
        actions: PackageActions
    } = PackageTableItems(t,deleteItem, redirect,disablePackage);





    const form = reactive({
        name: "",
        duration: "",
        unit:"",
        status:"",
        price:"",
    });

    const getSubscriptions = async (subscriptionQuery) => {
        if (itemData.value) {
        try {
                if (subscriptionQuery === undefined)
                    subscriptionQuery = {
                        client_id:  '',
                        search: '',
                        sort: '',
                        page: 1,
                        per_page: 10,
                    }

                SubscriptionsIsLoading.value = true

                const {data: {data, meta}} = await SubscriptionService.index({
                    package_id:   itemData.value.id,
                    client_id:  '',
                    page: subscriptionQuery.page,
                    sort: subscriptionQuery.sort,
                    size: subscriptionQuery.per_page,
                    search: subscriptionQuery.search,
                });
                Subscriptions.value = data
                subscriptionPagination.value = {
                    ...subscriptionPagination.value,
                    page: subscriptionQuery.page,
                    total: meta.total,
                    per_page: subscriptionQuery.per_page,
                }
                cookie.set(`${PackageService.routPath}Load${itemData.value.id}SubscriptionsData`, JSON.stringify({
                    pagination: subscriptionPagination.value,
                    query: subscriptionQuery
                }));
            SubscriptionsIsLoading.value = false
            }catch(error)
            {
                SubscriptionsIsLoading.value = false
                await errorHandle(error)
            }
        }
    }

    const validation = {
        name: [
            validationRules.required
        ],
        duration: [
            validationRules.required,
            validationRules.integer,
            validationRules.minValue(1)
        ],
        unit: [
            validationRules.required
        ],
        status: [
            validationRules.required
        ],
        price: [
            validationRules.required
        ],

    }


    return {
        SubscriptionsIsLoading,
        itemData,
        tableData,
        pagination,
        query,
        packages,
        isLoading,
        validation,
        form,
        valid,
        parent,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        getItem,
        loadData,
        service,
        loadParentData,
        storeItem,
        parentDetails,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        PackageActions,
        PackageCols,
        getPackageStatus,
        getUnitStatus,
        getSubscriptionStatus,
        unitStatus,
        packageStatus,
        subscriptionStatus,
        getSubscriptions,
        Subscriptions,
        subscriptionPagination,
        subscriptionQuery,
        t,
        tabs,
        redirect,
        disablePackage,
        disableSubscription,
    }
}
