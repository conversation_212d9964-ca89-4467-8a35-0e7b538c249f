<?php

namespace App\Models;

use App\Enums\SubscriptionStatusEnum;
use App\Traits\BaseTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Laravel\Scout\Searchable;

class Subscription extends BaseModel
{
    use Searchable;

    protected $table = 'subscriptions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'package_id',
        'from',
        'to',
        'is_paid',
        'status',
    ];

    /**
     * Define search keys for <PERSON><PERSON> Scout
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'from' => $this->from,
            'to' => $this->to,
            'status' => $this->status,
            'is_paid' => $this->is_paid,
        ];
    }

    /**
     * Get the client that owns the subscription
     *
     * @return BelongsTo
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the package that owns the subscription
     *
     * @return BelongsTo
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    /**
     * Check if the subscription is active
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === SubscriptionStatusEnum::ACTIVE->value;
    }

    /**
     * Check if the subscription is expired
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->status === SubscriptionStatusEnum::EXPIRED->value;
    }

    /**
     * Check if the subscription is disabled
     *
     * @return bool
     */
    public function isDisabled(): bool
    {
        return $this->status === SubscriptionStatusEnum::DISABLED->value;
    }

    /**
     * Expire the subscription
     *
     * @return bool
     */
    public function expire(): bool
    {
        return $this->update([
            'status' => SubscriptionStatusEnum::EXPIRED->value,
            'to' => Carbon::now()->format('Y-m-d')
        ]);
    }

    /**
     * Check if the subscription has expired based on the end date
     *
     * @return bool
     */
    public function hasExpired(): bool
    {
        return Carbon::parse($this->to)->isPast() && $this->isActive();
    }

    /**
     * Disable the subscription
     *
     * @return bool
     */
    public function disable(): bool
    {
        return $this->update([
            'status' => SubscriptionStatusEnum::DISABLED->value,
            'to' => Carbon::now()->format('Y-m-d')
        ]);
    }

    /**
     * Get the remaining days of the subscription
     *
     * @return int
     */
    public function getRemainingDays(): int
    {
        if (!$this->isActive()) {
            return 0;
        }

        $endDate = Carbon::parse($this->to);
        $today = Carbon::now();

        if ($today->gt($endDate)) {
            return 0;
        }

        return $today->diffInDays($endDate);
    }

    /**
     * Check if the subscription is valid (active and not expired)
     *
     * @return bool
     */
    public function isValid(): bool
    {
        return $this->isActive() && !$this->hasExpired();
    }

    public function scopeActive()
    {
        return $this->where('status', SubscriptionStatusEnum::ACTIVE->value);
    }

    public function scopeDuringMonthOffset(Builder $query, int $monthsAgo)
    {
        $startOfMonth = Carbon::now()->subMonthNoOverflow($monthsAgo)->startOfMonth();
        $endOfMonth = Carbon::now()->subMonthNoOverflow($monthsAgo)->endOfMonth();

        return $query->whereBetween('created_at', [$startOfMonth, $endOfMonth]);
    }

    public static function store($package)
    {
        $subscription = Subscription::create([
            'client_id' => auth()->id(),
            'from' => now(),
            'to' => now()->addDays($package->duration),
            'is_paid' => 1,
            'status' => SubscriptionStatusEnum::ACTIVE->value
        ]);
        return $subscription;
    }
}
