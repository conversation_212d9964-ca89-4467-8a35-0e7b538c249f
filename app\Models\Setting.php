<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;

class Setting extends Model
{
    use  Searchable;

    protected $table = 'settings';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'value',
        'code',
        'type',
    ];

    /**
     * @return string[]
     */
    public function toSearchableArray(): array
    {
        return [
            'name'  => '',
            'value' => '',
            'code'  => '',
            'type'  => '',
        ];
    }
}
