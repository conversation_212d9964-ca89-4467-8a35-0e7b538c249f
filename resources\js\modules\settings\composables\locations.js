import {reactive, ref} from 'vue'
import LocationsService from "@/services/locations-service.js";
import useShared from "@/helpers/shared.js";
import locationTableItems from '../models/location-table-items';

export default function useLocations() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        innerService,
        itemData,
        getItem,
        parent,
        storeItem,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadData,
        loadParentData,
        deleteItem,
        cancel,
        saveItem,
        router,
        userPermissions,
        t,
        redirect
    } = useShared()

    service.value = LocationsService;
    innerService.value = LocationsService;

    const location = ref();
    const autoLocations = ref([]);
    const autoProtocols = ref([]);

    const getAutoLocationLabel = (id) => {
        const autoLocation = autoLocations.value.find(ft => ft.id === id);
        return  autoLocation?autoLocation.name:'';
    };

    const getAutoProtocolLabel = (id) => {
        const autoProtocol = autoProtocols.value.find(ft => ft.id === id);
        return  autoProtocol?autoProtocol.name:'';
    };

    const {
        cols: locationCols,
        actions: locationActions
    } = locationTableItems(t, redirect, showUpdateModal, deleteItem,getAutoLocationLabel,getAutoProtocolLabel);

    const form = reactive({
        name: "",
        code: "",
        auto_location_id:"",
        autoLocations:[],
        auto_protocol_id:"",
        autoProtocols:[],
        free_servers_count: 0,
        premium_servers_count: 0,
    });

    const validation = {
        name: [
            validationRules.required
        ],
        code: [
            validationRules.required
        ],
        free_servers_count: [
            validationRules.required,
            validationRules.integer,
            validationRules.minValue(0)
        ],
        premium_servers_count: [
            validationRules.required,
            validationRules.integer,
            validationRules.minValue(0)
        ],
    }

    const getAutoProtocols = async () => {
        try {
            const response = await service.value.getAutoProtocols();
            autoProtocols.value = response.data.data;
        } catch (error) {
            await errorHandle(error);
        }
    };

    const getAutoLocations = async () => {
        try {
            const response = await service.value.getAutoLocations();
            autoLocations.value = response.data.data;
        } catch (error) {
            await errorHandle(error);
        }
    };


    return {
        itemData,
        tableData,
        pagination,
        query,
        location,
        isLoading,
        validation,
        form,
        valid,
        parent,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        getItem,
        loadData,
        service,
        loadParentData,
        storeItem,
        parentDetails,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        locationCols,
        locationActions,
        autoLocations,
        getAutoLocations,
        getAutoProtocols,
        autoProtocols,
        getAutoLocationLabel,
        getAutoProtocolLabel
    }
}
