<?php

namespace App\Http\Resources\MobileResources;

use AllowDynamicProperties;
use App\Models\Server;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

#[AllowDynamicProperties] class LocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->is_locked = isset($this->is_locked) ? $this->is_locked : 0;
        $servers_count = auth()->user()->isPremium() ? $this->premium_servers_count : $this->free_servers_count;
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'is_locked' => $this->is_locked,
            'servers_count' => $this->is_locked ? 0 : $servers_count
        ];
    }
}
