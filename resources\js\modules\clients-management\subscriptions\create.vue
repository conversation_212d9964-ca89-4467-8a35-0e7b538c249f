<template>
    <v-container>
        <v-card width="90%" class="pa-5 mx-auto" color="white" outlined>
            <v-card-title class="headline black" primary-title>
                <h3>
                    {{ $t('subscriptions.add') }}
                </h3>
            </v-card-title>
            <v-card-text class="pa-5">
                <v-form
                    v-model="valid"
                    v-on:submit.prevent="emit('create', {...createForm}, 'clients', true)"
                >
                    <v-row class="mtn-2">
                        <v-col>
                            <v-select
                                v-model="createForm.package_id"
                                :items="packages"
                                :label="$t('subscriptions.package')"
                                variant="outlined"
                                :rules="validationSubscription.package_id"
                                item-title="name"
                                item-value="id"
                            ></v-select>
                        </v-col>
                        </v-row>
                    <v-btn
                        @click="emit('cancel')"
                        :class="'float-' + $t('right')"
                        class="colored-btn-cancel"
                    >
                        {{ $t("cancel") }}
                    </v-btn>
                    <v-btn
                        :class="'float-' + $t('right') + ' colored-btn'"
                        type="submit"
                    >
                        <span class="px-2">{{ $t("save") }}</span>
                    </v-btn>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue';
import useClients from "@/modules/clients-management/composables/clients";



const valid = defineModel("valid", {type: Boolean});
const props = defineProps(["validationSubscription"]);
const emit = defineEmits(["cancel", "create"]);


const form = reactive({
    package_id: '',
});



const createForm = ref({...form.value});
const {
    packages,
    getPackages,
    itemData,
    validationSubscription
} = useClients()


onMounted(async () => {
    await  getPackages();
})
</script>


