<?php

namespace App\Http\Requests\VpnRequests;

use App\Http\Requests\BaseRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateConnectionsCountRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $serverId = auth()->user()->id;
        return [
            'port' => [
                'required',
                'numeric',
                'integer',
                'min:0',
                'max:65535',
                Rule::exists('ports')->where('server_id', $serverId)
            ],
            'connections_count' => [
                'required',
                'integer'
            ]
        ];
    }
}
