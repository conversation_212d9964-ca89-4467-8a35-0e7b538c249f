import ExpenseIndex from "@/modules/expenses-management/expenses/index.vue";
import ExpenseCreate from "@/modules/expenses-management/expenses/create.vue";
import ExpenseUpdate from "@/modules/expenses-management/expenses/update.vue";
import ExpenseDetails from "@/modules/expenses-management/expenses/details.vue";

const ExpensesManagementRoutes = [
    {
        path: '/expenses',
        name: 'expenses',
        component: ExpenseIndex,
        meta: {
            breadcrumb: 'expenses.expenses'
        }
    },
    {
        path: '/expenses/create',
        name: 'expenses/create',
        component: ExpenseCreate,
        props: true,
        meta: {
            breadcrumb: 'expenses.add'
        }
    },
    {
        path: "/expenses/:id/update",
        name: 'expenses/update',
        component: ExpenseUpdate,
        props: true,
        meta: {
            breadcrumb: 'expenses.edit'
        }
    },
    {
        path: "/expenses/:id/details",
        name: 'expenses/details',
        component: ExpenseDetails,
        props: true,
        meta: {
            breadcrumb: 'expenses.details'
        }
    },
]

export default ExpensesManagementRoutes;