<?php

namespace App\Http\Resources\MobileResources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'is_free' => $this->is_free,
            'internal_status' => $this->internal_status,
            'external_status' => $this->external_status,
            'health_status' => $this->health_status,

            'protocols' => $this->whenLoaded('protocols', function() {
                return ServerProtocolsResource::collection($this->protocols);
                    }),      
        ];
    }
}
