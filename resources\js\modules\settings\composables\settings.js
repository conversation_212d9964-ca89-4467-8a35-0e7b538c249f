import {reactive, ref} from 'vue'
import SettingsService from "@/services/settings-service.js";
import useShared from "@/helpers/shared.js";
import settingTableItems from '../models/setting-table-items';
import contentTableItems from "@/modules/settings/models/content-table-items";
import cookie from "vue-cookies";

export default function useSettings() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        storeItem,
        loadData,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadParentData,
        deleteItem,
        errorHandle,
        cancel,
        saveItem,
        router,
        userPermissions,
        t,
        cookie,
        redirect
    } = useShared()

    service.value = SettingsService;

    const setting = ref();

    const {
        cols: settingCols,
        actions: settingActions
    } = settingTableItems(t, redirect, showUpdateModal);


    const {
        cols: contentCols,
        actions: contentActions
    } = contentTableItems(t, redirect, showUpdateModal);

    const form = reactive({
        name: "",
        type: "",
        value:"",
    });


    const loadContentData = async (query) => {
        try {
            isLoading.value = true;
            if (query === undefined)
                query = {
                    search: '',
                    page: 1,
                    per_page: 10,
                }
            const {data: {data, meta}} = await service.value.indexContent({
                parent_id: '',
                page: query.page,
                size: query.per_page,
                sort: query.sort,
                search: query.search,
            });
            tableData.value = data
            pagination.value = {...pagination.value, page: query.page, total: meta.total}
            cookie.set(`index-contentloadContentData`, JSON.stringify({pagination: pagination.value, query: query}));
            isLoading.value = false
        } catch (error) {
            isLoading.value = false
            await errorHandle(error)
        }
    }


    const validation = {
        name: [
            validationRules.required
        ],
        type: [
            validationRules.required
        ],
        value: [
            validationRules.required
        ],
    }


    return {
        itemData,
        tableData,
        pagination,
        query,
        setting,
        isLoading,
        validation,
        form,
        valid,
        parent,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        getItem,
        loadData,
        service,
        loadParentData,
        storeItem,
        parentDetails,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        settingActions,
        settingCols,
        contentActions,
        contentCols,
        loadContentData,
    }
}
