
<template>
    <v-row class="mt-4">
        <v-col>

      <v-tabs
        v-model="tabs"
        bg-color="primary"
      >
        <v-tab value="devices">{{ $t('clients.devices') }}</v-tab>
        <v-tab value="subscriptions">{{ $t('clients.subscriptions') }}</v-tab>
        <v-tab value="payments">{{ $t('clients.payments') }}</v-tab>
        <v-tab value="sessions">{{ $t('clients.sessions') }}</v-tab>
      </v-tabs>

      <v-card-text>
        <v-tabs-window v-model="tabs">
          <v-tabs-window-item value="devices">
            <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <t-data-table
            v-if="itemData"
                :rows="devicesTableData"
                :pagination="devicesPagination"
                :query="devicesQuery"
                :loading="devicesIsLoading"
                :queryType="`Load${props.itemData.id}DevicesData`"
                :userPermissions="userPermissions"
                :cols="devicesCols"
                :actions="devicesActions"
                @loadData="loadDevicesData"
            >
            </t-data-table>
        </div>
          </v-tabs-window-item>

          <v-tabs-window-item value="subscriptions">

              <t-modal v-model:show="storeModal" :width="'70%'">
                  <create-subscriptions
                      v-model:form="form"
                      v-model:valid="valid"
                      :validation="validation"
                      @create="storeModalItemSubscriptionForClient"
                      @cancel="cancel()"
                  />
              </t-modal>
              <v-btn
                  v-if="userPermissions.includes('subscriptions/create')"
                  @click="showStoreModal()" :class="'float-'+$t('right') + ' colored-btn'" >
                  <span class="px-2">{{ $t('subscriptions.add') }}</span>
                  <img class="crud-icon" src="@/assets/icons/ic_add_2.svg">
              </v-btn>
                  <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
                      <t-data-table
                          :rows="subscriptionsTableData"
                          :pagination="subscriptionsPagination"
                          :query="subscriptionQuery"
                          :loading="subscriptionsIsLoading"
                          :queryType="`Load${props.itemData.id}SubscriptionsData`"
                          :userPermissions="userPermissions"
                          :cols="subscriptionsCols"
                          :actions="subscriptionsActions"
                          @loadData="loadSubscriptionsData"
                      >
                      </t-data-table>
                  </div>
          </v-tabs-window-item>

          <v-tabs-window-item value="payments">
            payments
          </v-tabs-window-item>
          <v-tabs-window-item value="sessions">
            <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
              <t-data-table
                  :rows="sessionsTableData"
                  :pagination="sessionsPagination"
                  :query="sessionsQuery"
                  :loading="sessionsIsLoading"
                  :queryType="`Load${props.itemData.id}SessionsData`"
                  :userPermissions="userPermissions"
                  :cols="sessionsCols"
                  :actions="sessionsActions"
                  @loadData="loadSessionsData"
              >
              </t-data-table>
            </div>
          </v-tabs-window-item>
        </v-tabs-window>
      </v-card-text>
        </v-col>

    </v-row>

  </template>

<script setup>
import { onMounted, watch } from 'vue';
import useClients from "../../composables/clients.js";
import devicesTableItems from '../../models/devices-table-items';
import TDataTable from "@/shared/components/t-data-table.vue";
import SubscriptionsTableItems from "@/modules/clients-management/models/subscriptions-table-items";
import sessionsTableItems from '../../models/sessions-table-items';
import TModal from "@/shared/components/t-modal.vue";
import CreateSubscriptions  from "@/modules/clients-management/subscriptions/create.vue";

const props = defineProps({
  itemData: {
        required: true,
        type: Object
    }
})

const {
    loadDevicesData,
    tabs,
    devicesTableData,
    devicesPagination,
    devicesQuery,
    devicesIsLoading,
    subscriptionsIsLoading,
    subscriptionsPagination,
    subscriptionsTableData,
    subscriptionQuery,
    loadSubscriptionsData,
    sessionsTableData,
    sessionsPagination,
    sessionsQuery,
    sessionsIsLoading,
    loadSessionsData,
    deleteItem,
    itemData,
    t,
    redirect,
    userPermissions,
    cancel,
    storeModalItemSubscriptionForClient,
    valid,
    validation,
    form,
    storeModal,
    showStoreModal,
    disableSubscription,
} = useClients();



const {
        cols: devicesCols,
        actions: devicesActions
    } = devicesTableItems(t, deleteItem, redirect);

const {
    cols: subscriptionsCols,
    actions: subscriptionsActions
} = SubscriptionsTableItems(t, deleteItem, redirect, disableSubscription);

const {
    cols: sessionsCols,
    actions: sessionsActions
} = sessionsTableItems(t, deleteItem, redirect);

onMounted(() => {
   itemData.value = props.itemData
  //  await loadDevicesData()

   // Watch for tab changes to load data when needed
  //  watch(tabs, async (newTab) => {
  //     if (newTab === 'sessions') {
  //        await loadSessionsData()
  //     } else if (newTab === 'subscriptions') {
  //        await loadSubscriptionsData()
  //     } else if (newTab === 'devices') {
  //        await loadDevicesData()
  //     }
  //  })
});
</script>
