import BaseService from "./base-service";
import axios from "axios";
import authHeader from "./auth-header";

class StatisticsService extends BaseService {
    routPath = '/statistics';

    constructor() {
        super();
    }

    clients(showLoader) {
        return axios.get(this.routPath + '-clients'
            ,{headers: authHeader(), showLoader: showLoader}
        );
    }

}

export default new StatisticsService();
