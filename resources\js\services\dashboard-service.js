import axios from "axios";
import BaseService from "./base-service";
import authHeader from "./auth-header";

class DashboardService extends BaseService {
    routPath = '/dashboard';

    constructor() {
        super();
    }

    getSubscriptions(showLoader) {
        return axios.get(this.routPath + '/subscriptions'
            , { headers: authHeader(), showLoader }
        );
    }

    getExpenses(showLoader) {
        return axios.get(this.routPath + '/expenses'
            , { headers: authHeader(), showLoader }
        );
    }

    getNotifications(showLoader) {
        return axios.get(this.routPath + '/notifications'
            , { headers: authHeader(), showLoader }
        );
    }

    getServers(showLoader) {
        return axios.get(this.routPath + '/servers'
            , { headers: authHeader(), showLoader }
        );
    }
}

export default new DashboardService();
