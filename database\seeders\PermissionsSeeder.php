<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            [
                'name_ar' => 'عرض قائمة الأدوار',
                'name' => 'roles'],
            [
                'name_ar' => 'إنشاء دور',
                'name' => 'roles/create',
            ],
            [
                'name_ar' => 'تعديل دور',
                'name' => 'roles/update',
            ],
            [
                'name_ar' => 'حذف دور',
                'name' => 'roles/delete',
            ],
            [
                'name_ar' => 'عرض تفاصيل دور',
                'name' => 'roles/details',
            ],
            [
                'name_ar' => 'عرض قائمة الصلاحيات',
                'name' => 'permissions',
            ],
            [
                'name_ar' => 'عرض قائمة المواقع',
                'name' => 'locations',
            ],
            [
                'name_ar' => 'إنشاء موقع',
                'name' => 'locations/create',
            ],
            [
                'name_ar' => 'تعديل موقع',
                'name' => 'locations/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل موقع',
                'name' => 'locations/details',
            ],
            [
                'name_ar' => 'حذف موقع',
                'name' => 'locations/delete',
            ],

            [
                'name_ar' => 'عرض قائمة المستخدمين',
                'name' => 'users',
            ],
            [
                'name_ar' => 'إنشاء مستخدم',
                'name' => 'users/create',
            ],
            [
                'name_ar' => 'تعديل مستخدم',
                'name' => 'users/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل مستخدم',
                'name' => 'users/details',
            ],
            [
                'name_ar' => 'حذف مستخدم',
                'name' => 'users/delete',
            ],
            [
                'name_ar' => 'تفعيل / إلغاء تفعيل مستخدم',
                'name' => 'users/toggle-activation',
            ],
            [
                'name_ar' => 'عرض قائمة البروتوكولات',
                'name' => 'protocols',
            ],
            [
                'name_ar' => 'إنشاء بروتوكول',
                'name' => 'protocols/create',
            ],
            [
                'name_ar' => 'تعديل بروتوكول',
                'name' => 'protocols/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل بروتوكول',
                'name' => 'protocols/details',
            ],
            [
                'name_ar' => 'عرض قائمة الخوادم',
                'name' => 'servers',
            ],
            [
                'name_ar' => 'إنشاء خادم',
                'name' => 'servers/create',
            ],
            [
                'name_ar' => 'تعديل خادم',
                'name' => 'servers/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل خادم',
                'name' => 'servers/details',
            ],
            [
                'name_ar' => 'حذف خادم',
                'name' => 'servers/delete',
            ],
                      /** Providers **/

            [
                'name_ar' => 'عرض قائمة المزودات',
                'name' => 'providers',
            ],
            [
                'name_ar' => 'إنشاء مزود',
                'name' => 'providers/create',
            ],
            [
                'name_ar' => 'تعديل مزود',
                'name' => 'providers/update',
            ],
            [
                'name_ar' => 'حذف مزود',
                'name' => 'providers/delete',
            ],

                      /** Ports **/

            [
                'name_ar' => 'عرض قائمة المنافذ',
                'name' => 'ports',
            ],
            [
                'name_ar' => 'إنشاء منفذ',
                'name' => 'ports/create',
            ],
            [
                'name_ar' => 'تعديل منفذ',
                'name' => 'ports/update',
            ],
            [
                'name_ar' => 'عرض منفذ',
                'name' => 'ports/details',
            ],
            [
                'name_ar' => 'حذف منفذ',
                'name' => 'ports/delete',
            ],

            /** Expenses **/
            [
                'name_ar' => 'عرض قائمة النفقات',
                'name' => 'expenses',
            ],
            [
                'name_ar' => 'إنشاء نفقة',
                'name' => 'expenses/create',
            ],
            [
                'name_ar' => 'تعديل نفقة',
                'name' => 'expenses/update',
            ],
            [
                'name_ar' => 'عرض نفقة',
                'name' => 'expenses/details',
            ],
            [
                'name_ar' => 'حذف نفقة',
                'name' => 'expenses/delete',
            ],
                    /** Groups **/
            [
                'name_ar' => 'عرض قائمة المجموعات',
                'name' => 'groups',
            ],
            [
                'name_ar' => 'إنشاء مجموعة',
                'name' => 'groups/create',
            ],
            [
                'name_ar' => 'تعديل مجموعة',
                'name' => 'groups/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل مجموعة',
                'name' => 'groups/details',
            ],
            [
                'name_ar' => 'حذف مجموعة',
                'name' => 'groups/delete',
            ],

                      /** clients **/

            [
                'name_ar' => 'عرض قائمة العملاء',
                'name' => 'clients',
            ],
            [
                'name_ar' => 'إنشاء عميل',
                'name' => 'clients/create',
            ],
            [
                'name_ar' => 'تعديل عميل',
                'name' => 'clients/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل عميل',
                'name' => 'clients/details',
            ],
            [
                'name_ar' => 'عرض جهاز العميل',
                'name' => 'clients/devices',
            ],
            [
                'name_ar' => 'حذف عميل',
                'name' => 'clients/delete',
            ],
            [
                'name_ar' => 'عرض جلسات العميل',
                'name' => 'clients/sessions',
            ],

                   /** Settings **/
            [
                'name_ar' => 'عرض قائمة الإعدادات',
                'name' => 'settings',
            ],
            [
                'name_ar' => 'عرض قائمة المحتويات',
                'name' => 'settings/indexContent',
            ],
            [
                'name_ar' => 'تعديل المحتوى',
                'name' => 'settings/update-content',
            ],
            [
                'name_ar' => 'تعديل إعداد',
                'name' => 'settings/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل الإعداد',
                'name' => 'settings/details',
            ],
            [
                'name_ar' => 'لوحة التحكم',
                'name' => 'dashboard',
            ],
            /** Tickets **/

            [
                'name_ar' => 'عرض قائمة الشكاوي',
                'name' => 'tickets',
            ],
            [
                'name_ar' => 'معالجة شكوى',
                'name' => 'tickets/handle',
            ],
            [
                'name_ar' => 'عرض تفاصيل الشكوى',
                'name' => 'tickets/details',
            ],
            /** packages **/

            [
                'name_ar' => 'عرض قائمة الحزم',
                'name' => 'packages',
            ],
            [
                'name_ar' => 'إنشاء حزمة',
                'name' => 'packages/create',
            ],
            [
                'name_ar' => 'تعديل حزمة',
                'name' => 'packages/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل حزمة',
                'name' => 'packages/details',
            ],
            [
                'name_ar' => 'حذف حزمة',
                'name' => 'packages/delete',
            ],
            [
                'name_ar' => 'تعطيل حزمة',
                'name' => 'packages/disable',
            ],

                /** Subscriptions **/

            [
                'name_ar' => 'عرض قائمة الاشتراكات',
                'name' => 'subscriptions',
            ],
            [
                'name_ar' => 'إنشاء اشتراك',
                'name' => 'subscriptions/create',
            ],
            [
                'name_ar' => 'تعطيل اشتراك',
                'name' => 'subscriptions/disable',
            ],

            /** Notifications **/
            [
                'name_ar' => 'عرض قائمة الإشعارات',
                'name' => 'notifications',
            ],
            [
                'name_ar' => 'إنشاء إشعار',
                'name' => 'notifications/create',
            ],
            [
                'name_ar' => 'تعديل إشعار',
                'name' => 'notifications/update',
            ],
            [
                'name_ar' => 'عرض تفاصيل إشعار',
                'name' => 'notifications/details',
            ],
            [
                'name_ar' => 'حذف إشعار',
                'name' => 'notifications/delete',
            ],
            /** Client Notifications **/
            [
                'name_ar' => 'عرض قائمة إشعارات الزبائن',
                'name' => 'client-notifications',
            ],

             /** Reports **/
             [
                'name_ar' => 'عرض تقارير الخوادم',
                'name' => 'reports/servers',
             ],
             [
                'name_ar' => 'عرض تقارير التنبيهات',
                'name' => 'reports/alerts',
             ],
            /** Client Statistics **/
            [
                'name_ar' => 'عرض إحصائيات المستخدمين',
                'name' => 'statistics/clients',
            ]
        ];

        foreach ($permissions as $permission) {
            Permission::query()->firstOrCreate(
                ['name' => $permission['name']],
                [
                    'name_ar' => $permission['name_ar'],
                    'name' => $permission['name'],
                ]
            );
        }

        $admin = User::where('email', '<EMAIL>')->first();

        $superAdmin = Role::firstOrCreate(
            ['name' => 'SuperAdmin'],
            [
                'name' => 'SuperAdmin',
                'name_ar' => 'مدير النظام',
                'created_by' => $admin->id
            ]
        );

        $superAdmin->syncPermissions(Permission::all());

        $admin->assignRole([$superAdmin]);
    
    }
}
