<template>
    <div>
        <v-btn
            v-if="userPermissions.includes('ports/create')"
            :class="'float-' + $t('right') + ' colored-btn'" :to="{ name: 'ports/create',
            params:{ server_id: props.id, server_name: props.itemData.name } }">
            <span class="px-2">{{ $t('ports.add') }}</span>
            <img class="crud-icon" src="@/assets/icons/ic_add_2.svg">
        </v-btn>

        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <t-data-table
                v-if="parent"
                :rows="portsTableData"
                :pagination="portsPagination"
                :query="portsQuery"
                :loading="portsIsLoading"
                :queryType="queryTypeState"
                :userPermissions="userPermissions"
                :cols="portsCols"
                :actions="portsActions"
                @loadData="loadPortsData"
            >
            </t-data-table>
        </div>
    </div>
</template>

<script setup>
import { inject, onMounted } from 'vue';
import usePorts from "../../../composables/ports.js";
import TDataTable from "@/shared/components/t-data-table.vue";
import store from "@/store/store.js";

const t = inject('t');
const currentUser = store.state.auth.user;
const userPermissions = currentUser ? currentUser.permissions : [];

const props = defineProps({
    itemData: {
        required: true,
        type: Object
    },
    id: {
        required: true,
        type: String
    }
});

const {
    query: portsPagination,
    pagination: portsQuery,
    isLoading: portsIsLoading,
    portsCols,
    portsActions,
    tableData: portsTableData,
    queryTypeState,
    parent,
    loadPortsData,
} = usePorts(props.id);

onMounted(() => {
    parent.value = props.id;
});
</script>
