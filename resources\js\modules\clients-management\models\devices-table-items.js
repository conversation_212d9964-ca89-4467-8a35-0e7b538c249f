

export default function devicesTableItems(t, deleteItem, redirect) {
    

    const cols = [
        { header:'device_id', field: 'devices.device_id', cell: (item) => item.device_id },
        { header:'device_os', field: 'devices.device_os', cell: (item) => item.device_os },
        { header:'device_os_version', field: 'devices.device_os_version', cell: (item) => item.device_os_version },
        { header:'app_version', field: 'devices.app_version', cell: (item) => item.app_version },
        { header:'fcm_token', field: 'devices.fcm_token', cell: (item) => item.fcm_token },
        { header:'last_connection_at_formatted', field: 'devices.last_connection_at', cell: (item) => item.last_connection_at_formatted },

    ];

    const actions = [
        
    ];


    return {
        cols,
        actions
    }
}