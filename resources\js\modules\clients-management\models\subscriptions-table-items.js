
export default function SubscriptionsTableItems(t, deleteItem, redirect,disableSubscription) {


    const cols = [
        { header:'packages.name.package_id', field: 'subscriptions.package', cell: (item) => item.package },
        { header:'from', field: 'subscriptions.from', cell: (item) => item.from },
        { header:'to', field: 'subscriptions.to', cell: (item) => item.to },
        { header:'status', field: 'subscriptions.status', cell: (item) => item.status },
        { header:'is_paid', field: 'subscriptions.is_paid', cell: (item) =>  t('boolean_type.'+item.is_paid) },

    ];

    const actions = [
        {
            header: 'disable',
            perm: 'subscriptions/disable',
            icon: "mdi-stop",
            action: (item) => disableSubscription(item.id),
            visible: (item) => item.status === 'active'
        }
    ];


    return {
        cols,
        actions
    }
}
