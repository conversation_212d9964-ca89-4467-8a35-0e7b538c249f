<?php

namespace App\Http\Controllers\MobileControllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\MobileResources\ProtocolResource;
use App\Http\Resources\MobileResources\ProtocolsListResource;
use App\Models\Protocol;
use Illuminate\Http\Request;

class ProtocolController extends Controller
{

    /**
     */
    public function index()
    {
        $protocols = Protocol::query()->get();
        return self::MobileResponse('success', ProtocolsListResource::collection($protocols));
    }

    /**
     * @urlParam code string required The code of the Setting.Example: wireguard, openvpn_udp, openvpn_tcp
     */
    public function show($code)
    {
        $protocol = Protocol::where('code', $code)->firstOrFail();
        return response($protocol->template)
            ->header('Content-Type', 'text/plain')
            ->header('Accept', 'text/plain')
            ->header('X-Protocol-Version', $protocol->version);
    }

}
