<?php

namespace App\Models;

use App\Enums\ClientTypesEnum;
use App\Helpers\MailHelper;
use App\Http\Requests\MobileRequests\ResetPasswordRequest;
use App\Mail\PasswordResetMail;
use App\Mail\VerificationCodeMail;
use App\Services\MailService;
use App\Models\Sanctum\PersonalAccessToken;
use App\Traits\BaseTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use DateTimeInterface;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\NewAccessToken;
use <PERSON><PERSON>\Scout\Searchable;

class Client extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, Notifiable, BaseTrait, SoftDeletes, HasFactory, Searchable;

    protected $guarded = [];

    protected $hidden = [
        'password',
        'remember_token',
    ];


    protected $appends = [
        'registered_at_formatted',
        'dropdown_name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'registered_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {

        return [
            'username' => '',
            'email' => '',
            'registered_at' => ''
        ];
    }

    public static function relations()
    {
        return [
            'devices',
        ];
    }

    public function getRegisteredAtFormattedAttribute()
    {
        return $this->registered_at?->format('Y-m-d\TH:i');
    }

    public function getdropdownNameAttribute()
    {
        return __('messages.clients.client') . ': '. $this->username . ', ' . $this->email . ', '
         . ClientTypesEnum::ENUM_DATA[$this->type]['title_'.app()->getLocale()];
    }

    public function getRegisteredDateAttribute()
    {
        return $this->registered_at?->format('Y-m-d');
    }

    public function lastAccessToken()
    {
        return $this->morphOne(PersonalAccessToken::class, 'tokenable')->latest('last_used_at');
    }

    public function devices()
    {
        return $this->hasMany(Device::class);
    }

    public function groups()
    {
        return $this->belongsToMany(Group::class, 'group_clients');
    }

    public function token()
    {
        $token = $this->createClientToken('app');
        return $token;
    }

    public function tokenize(): array
    {
        $token = $this->token($this->username);

        return [
            'id' => $this->id,
            'email' => $this->email,
            'username' => $this->username,
            'type' => $this->type,
            'username' => $this->username,
            'access_token' => $token->plainTextToken,
            'secret_key' => $token->accessToken->secret_key,
            'country' => $token->accessToken->country
        ];
    }

    public function ExternalLogins()
    {
        return $this->hasMany(ExternalLogin::class);
    }

    /**
     * Create a new personal access token for the user.
     *
     * @param  string  $name
     * @param  array  $abilities
     * @param  \DateTimeInterface|null  $expiresAt
     * @return \Laravel\Sanctum\NewAccessToken
     */
    public function createClientToken(string $name, array $abilities = ['*'], ?DateTimeInterface $expiresAt = null)
    {
        $plainTextToken = $this->generateTokenString();
        $currentCountry = $this->detectCountry(request()->ip());

        $token = $this->tokens()->create([
            'name' => $name,
            'secret_key' => hash('sha256', "$plainTextToken-secret_key"),
            'token' => hash('sha256', $plainTextToken),
            'abilities' => $abilities,
            'expires_at' => $expiresAt,
            'country_id' => $currentCountry['id'],
        ]);

        $token['country'] = $currentCountry->only(['id', 'name', 'code', 'auto_location_id', 'auto_protocol_id']);

        return new NewAccessToken($token, $token->getKey() . '|' . $plainTextToken);
    }

    public function detectCountry(string $ip)
    {
        $geoIpCountry = geoip($ip);
        $country = Location::where('code', $geoIpCountry['country_code2'])->first();

        if(!$country)
            $country = Location::where('code', config('geoip.default_location.country_code2'))->first();

        return $country;
    }

    public function deleteCurrentToken()
    {
        return $this->currentAccessToken()->delete();
    }

    /**
     * Check if a client is verified
     */
    public function isVerified()
    {
        return $this->is_verified;
    }

    public function isFree()
    {
        return $this->type == ClientTypesEnum::FREE->value;
    }

    public function isPremium()
    {
        return $this->type == ClientTypesEnum::PREMIUM->value;
    }

    public function currentCountry()
    {
        return $this->currentAccessToken()->country;
    }

    /**
     * Check if verification code of the client match with the entered verification code
     */
    public function matchVerificationCode($verificationCode)
    {
        return ($this->verification_code === $verificationCode);
    }

    /**
     * Verify the client
     */
    public function markAsVerified()
    {
        return $this->update([
            'is_verified' => true,
            'verification_code' => null
        ]);
    }

    public function matchResetPasswordVerificationCode($verificationCode)
    {
        $resetCodeRecord = DB::table('password_reset_codes')
            ->where('email', $this->email)
            ->firstOrFail();
        return ($resetCodeRecord->code === $verificationCode);
    }

    public function matchWithCurrentPassword($password)
    {
        return Hash::check($password, $this->password);
    }

    public function changePassword(string $newPassword)
    {
        $this->update([
            'password' => Hash::make($newPassword)
        ]);
        return true;
    }

    public function hasRelations(): bool
    {
        $this->loadCount($this->relations());
        foreach ($this->relations() as $key => $value) {
            if ($this[$value . '_count'])
                return true;
        }

        return false;
    }
}
