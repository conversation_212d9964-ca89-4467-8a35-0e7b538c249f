<?php

namespace App\Traits;
use Illuminate\Support\Facades\Schema;

trait GeneralSearch
{

    public function generalSearch($model_name, $query, $request,$table_name='')
    {

        if (isset($request->search) && $request->search !="undefined") {
            $columns = Schema::getColumnListing(($model_name)->getTable());
            $query->where(function ($query) use ($columns, $request,$table_name) {
                foreach ($columns as $column) {
                        $column = $table_name?$table_name.'.'.$column:$column;
                    $query->orWhere($column, 'like', "%{$request->search}%");
                }
            });
        }
        return $query;
    }

    public static function searchInAllColumns($query,$table_name,$request)
    {
        if (isset($request->search)) {
            $columns = Schema::getColumnListing($table_name);
            $query->where(function ($query) use ($columns, $request, $table_name) {
                foreach ($columns as $column) {
                    $column = $table_name ? $table_name . '.' . $column : $column;
                    $query->orWhere($column, 'like', "%{$request->search}%");
                }
            });
        }
    }

    public function customSearch($query, $request, $columns=[], $table_name=''){
        if (isset($request->search) && $request->search !="undefined") {
            $query->where(function ($query) use ($columns, $request,$table_name) {
                foreach ($columns as $column) {
                        $column = $table_name?$table_name.'.'.$column:$column;
                    $query->orWhere($column, 'like', "%{$request->search}%");
                }
            });
        }
        return $query;
    }
    
    public function orderBy($query, $request, $table)
    {
        $parts = $request->sort ? explode(':', $request->sort) : [];
        if(count($parts)){
        $isRelation = count($parts) > 1 && strpos($parts[0], '.') !== false;
        $relationPart = $isRelation ? explode('.', $parts[0]) : [$parts[0] ?? null];
        $relationTable = $isRelation ? $relationPart[0] : null;
        $column = $request->sort ? ($isRelation ? $relationPart[1] : $relationPart[0]) : 'id';
        $dir = $request->sort ? $parts[1] : 'desc';
        $tableField = $isRelation ? $relationPart[2] : '';
    
        if (isset($request->sort) && $request->sort != "undefined") {
            if (!$isRelation) {
                $query->orderBy($column, $dir);
            } else {

                $relationTableAlias = $relationTable; // Default alias

            // If the relation table is the same as the main table, create an alias
            if ($relationTable === $table) {
                $relationTableAlias = $relationTable . '_related'; // You can choose a different alias
            }

            $query = $query->leftJoin($relationTable . ' as ' . $relationTableAlias, $relationTableAlias.'.id', '=', $table.'.'.$tableField)
            ->orderBy($relationTableAlias.'.'.$column, $dir)
            ->select($table.'.*');
            }

        }
        }
        return $query;
    }

}
