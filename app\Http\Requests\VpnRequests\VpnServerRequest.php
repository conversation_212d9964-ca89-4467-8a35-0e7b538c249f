<?php

namespace App\Http\Requests\VpnRequests;

use App\Enums\ServerResources;
use App\Http\Requests\BaseRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class VpnServerRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */

     
    public function rules(): array
    {
        return [
            'resources' => ['required', 'array'],
            'resources.*.name' => ['required', 'string', Rule::in(ServerResources::asArray())],
            'resources.*.consumption' => ['required', 'numeric', 'decimal:0,2']
        ];
    }
}
