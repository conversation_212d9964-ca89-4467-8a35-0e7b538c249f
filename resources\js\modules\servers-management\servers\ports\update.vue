<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container v-if="!isLoading" class="font-weight-bold">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("ports.edit") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>


        <v-form
                v-model="valid"
                @submit.prevent="save"
            >
            <v-row>


            <v-col cols="12" md="6" lg="6">
                <label>{{ $t('ports.server') }}</label>
                <h4>
                   {{ itemData.server.name }}
                </h4>
            </v-col>



            <v-col cols="12" md="6" lg="6">
                    <v-autocomplete
                        v-model="itemData.protocol"
                        :items="protocols"
                        class="required"
                        item-value="id"
                        return-object
                        item-title="code"
                        :label="$t('ports.protocol')"
                        @click="getProtocols()"
                        variant="outlined"
                        dense
                        :rules="validation.protocol"
                        ></v-autocomplete>
            </v-col>


            <v-col cols="12" md="6" lg="6">
            <v-text-field
                    dense
                    v-model="itemData.ip"
                    class="required"
                    :label="$t('ports.ip')"
                    variant="outlined"
                    :rules="validation.ip"
                >
                </v-text-field>
            </v-col>


            <v-col cols="12" md="6" lg="6">
            <v-text-field
                    dense
                    v-model="itemData.port"
                    class="required"
                    :label="$t('ports.port')"
                    variant="outlined"
                    :rules="validation.port"
                >
                </v-text-field>
            </v-col>


            <v-col v-if="isWireGuard" cols="12" md="6" lg="6">
            <v-text-field
                    dense
                    v-model="itemData.control_ip_port"
                    :label="$t('ports.control_ip_port')"
                    variant="outlined"
                    class="required"
                    :rules="validation.control_ip_port"
                >
                </v-text-field>
            </v-col>

            <v-col cols="12" md="6" lg="6">
            <v-text-field
                    dense
                    type="number"
                    v-model="itemData.connections_threshold"
                    class="required"
                    :label="$t('ports.connections_threshold')"
                    variant="outlined"
                    :rules="validation.connections_threshold"
                >
                </v-text-field>
            </v-col>

            <v-col cols="12" md="6" lg="6">
                <v-select
                    v-model="itemData.status"
                        :items="Object.values(portStatus().enumData)"
                        item-value="value"
                        :item-title="`title_${$t('locale.lang')}`"
                        class="required"
                        variant="outlined"
                        :label="$t('ports.status')"
                        :rules="validation.status"
                ></v-select>
            </v-col>

            <v-col cols="12" md="6" lg="6">
                <v-text-field
                    dense
                    v-model="itemData.purpose"
                    :label="$t('ports.purpose')"
                    variant="outlined"
                    :rules="validation.purpose"
                >
                </v-text-field>
            </v-col>

            <v-col cols="12">
                    <v-textarea
                        v-if="isWireGuard"
                        v-model="itemData.server_public_key"
                        :label="$t('ports.server_public_key')"
                        variant="outlined"
                        class="required"
                        :rules="validation.server_public_key"
                    ></v-textarea>
                </v-col>

        </v-row>

        <router-link :to="{ name: 'servers/details', params: { id: itemData.server_id }}">
                <v-btn
                    :class="'float-' + $t('right')"
                    class="colored-btn-cancel"
                >
                    {{ $t("cancel") }}
                </v-btn>
            </router-link>
            <v-btn
                :class="'float-' + $t('right') + ' colored-btn'"
                type="submit"
            >
                <span class="px-2">{{ $t("save") }}</span>
                <img class="crud-icon" src="@/assets/icons/ic_add_2.svg" />
            </v-btn>

        </v-form>
    </v-container>
</template>
<script setup>
import usePorts from "../../composables/ports.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import {onMounted, computed} from "vue";
import portStatus from "@/enums/port-status-enum.js";
import protocolsEnum from "@/enums/protocols-enum.js";

const {
    isLoading,
    portTypes,
    getProtocols,
    protocols,
    getItem,
    validation,
    itemData, valid,
    router,
    updateItem,
    userPermissions,
} = usePorts(); // Use the new composable

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})



onMounted( async () => {
    await getProtocols();
    getItem(props.id, true)
})

const save = async () => {
    await updateItem({ ...itemData.value }, { name: 'servers/details', params: { id: itemData.value.server_id }});
}

const isWireGuard = computed(() => {
    return itemData.value?.protocol.code === protocolsEnum().cases.WIREGUARD;
})

</script>
