<?php

namespace App\Http\Requests\PortRequests;

use App\Enums\PortStatusEnum;
use App\Enums\ProtocolsEnum;
use App\Http\Requests\BaseRequest;
use App\Rules\IPPortRule;
use Illuminate\Validation\Rule;

class StorePortRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'server_id' => ['required', 'min:1', 'integer', 'exists_ignore_deleted:servers,id'],
            'protocol_id' => ['required', 'min:1', 'integer', 'exists_ignore_deleted:protocols,id',
                Rule::unique('ports')->where('server_id', $this->server_id)->whereNull('deleted_at')],

            'connections_threshold' => ['required', 'integer','min:0'],
            'ip' => ['required', 'string', 'ip', 'max:50'],
            'port' => [
                'required',
                'numeric',
                'integer',
                'min:0',
                'max:65535',
                Rule::unique('ports')->where('server_id', $this->server_id)
                ->where('ip', $this->ip)->whereNull('deleted_at')
            ],

            'status' => ['required', 'string', 'in:' . implode(',', PortStatusEnum::getValues())],
            'control_ip_port' => ['required_if:protocol.code,'. ProtocolsEnum::WIREGUARD->value,
             'max:60',
             new IPPortRule()],
            'purpose' => ['nullable', 'string', 'max:100'],
            'server_public_key' => ['required_if:protocol.code,'. ProtocolsEnum::WIREGUARD->value]
        ];
    }

    public function prepareForValidation()
    {
        $isWireGuard = $this->protocol['code'] === ProtocolsEnum::WIREGUARD->value;
        $this->merge([
            'protocol_id' => $this->protocol['id'],
            'server_public_key' => $isWireGuard ? $this->server_public_key : null,
            'control_ip_port' => $isWireGuard ? $this->control_ip_port : null
        ]);
    }
}
