<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container v-if="!isLoading" class="font-weight-bold">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("groups.details") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <v-row>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("groups.name") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.name }}
                </b>
            </v-col>

            </v-row>

            <v-row>
                <v-col cols="12">
                    <h3> {{ $t('groups.clients') }}</h3>
        <v-divider :thickness="2" class="mt-3 mb-4"></v-divider>

        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <ClientsSelectTable
                :rows="tableData"
                :pagination="pagination"
                :query="query"
                :queryType="'LoadParentData' + id"
                :loading="isLoading"
                :userPermissions="userPermissions"
                :cols="clientCols"
                :actions="clientActions"
                :showActions="true"
                @loadData="loadGroupClients"
            >
            </ClientsSelectTable>
        </div>
            </v-col>
            </v-row>
                
            
            


    </v-container>
</template>
<script setup>
import useGroups from "../composables/groups.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import {onMounted} from "vue";
import ClientsSelectTable from "./includes/clientsSelectTable.vue";

const {
    tableData,
    loadGroupClients,
    parent,
    clientCols,
    pagination,
    query,
    clientActions,

    userPermissions,

    isLoading,
    getItem,
    validation,
    itemData, valid,
    router,
} = useGroups();

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

onMounted(() => {
    parent.value = props.id;
    getItem(props.id, true)
})

</script>
