<?php

namespace App\Models;

use App\Traits\BaseTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use <PERSON><PERSON>\Scout\Searchable;

class Notification extends Model
{
    use  Searchable,BaseTrait;


    protected $table = 'notifications';


    protected $fillable = [
        'status',
        'type',
        'content',
        'subject',
        'group_id',
        'scheduled_at',
    ];

    /**
     * @return BelongsTo
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class, 'group_id');
    }

    public function clients()
    {
        return $this->belongsToMany(Client::class);
    }

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'status'       => '',
            'subject'      => '',
            'type'         => '',
            'content'      => '',
            'scheduled_at'      => '',
        ];
    }
}
