const notificationsIndex = () => import ('./notifications/index.vue');
const notificationsCreate = () => import ('./notifications/create.vue');
const notificationsUpdate = () => import ('./notifications/update.vue');
const notificationsDetail = () => import ('./notifications/details.vue');


const NotificationsManagementRoutes = [
    {
        path: '/notifications',
        name: 'notifications',
        component: notificationsIndex,
        meta: {
            breadcrumb: 'notifications.notifications'
        }
    },
    {
        path: '/notifications/create',
        name: 'notifications/create',
        component: notificationsCreate,
        meta: {
            breadcrumb: 'notifications.add'
        }
    },
    {
        path: "/notifications/:id/update",
        name: 'notifications/update',
        component: notificationsUpdate,
        props: true,
        meta: {
            breadcrumb: 'notifications.edit'
        }
    },
    {
        path: "/notifications/:id/details",
        name: 'notifications/details',
        component: notificationsDetail,
        props: true,
        meta: {
            breadcrumb: 'notifications.details'
        }
    }
];


export default NotificationsManagementRoutes;
