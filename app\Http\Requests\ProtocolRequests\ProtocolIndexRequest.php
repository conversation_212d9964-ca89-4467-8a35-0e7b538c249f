<?php

namespace App\Http\Requests\ProtocolRequests;

use App\Http\Requests\IndexRequest;


class ProtocolIndexRequest extends IndexRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = parent::rules();
        return $rules;
    }
}
