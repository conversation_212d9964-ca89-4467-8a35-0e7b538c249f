<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum SubscriptionStatusEnum: string
{
    use EnumTrait;

    case ACTIVE      = 'active';
    case EXPIRED     = 'expired';
    case DISABLED    = 'disabled';

    const ENUM_DATA = [
        self::ACTIVE->value => [
            'title_en' => 'Active',
            'title_ar' => 'نشط',
            'value' => 'active',
        ],
        self::EXPIRED->value => [
            'title_en' => 'Expired',
            'title_ar' => 'منتهي',
            'value' => 'expired',
        ],
        self::DISABLED->value => [
            'title_en' => 'Disabled',
            'title_ar' => 'معطل',
            'value' => 'disabled',
        ],
    ];
}
