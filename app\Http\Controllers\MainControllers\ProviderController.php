<?php

namespace App\Http\Controllers\MainControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProviderRequests\IndexProviderRequest;
use App\Http\Requests\ProviderRequests\StoreProviderRequest;
use App\Http\Requests\ProviderRequests\UpdateProviderRequest;
use App\Http\Resources\Provider\ProviderResource;
use App\Models\Provider;
use Illuminate\Validation\ValidationException;


class ProviderController extends Controller
{

    function __construct()
    {
        $this->middleware('permission:providers', ['only' => ['index']]);
        $this->middleware('permission:providers/create', ['only' => ['store']]);
        $this->middleware('permission:providers/update', ['only' => ['update']]);
        $this->middleware('permission:providers/delete', ['only' => ['destroy']]);
    }
    /**
     * Display a listing of the resource.
     */
    public function index(IndexProviderRequest $request)
    {
        $query     = Provider::search($request->search)->query(function($query) use ($request){
            $query = $this->orderBy($query, $request, 'providers');
        });
        return ProviderResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    /**
     * @param StoreProviderRequest $request
     * @return false|string
     */
    public function store(StoreProviderRequest $request)
    {
        $provider = Provider::create($request->all());
        return self::jsonResponse('success', $provider);
    }

    /**
     * @param UpdateProviderRequest $request
     * @param Provider $provider
     * @return false|string
     */
    public function update(UpdateProviderRequest $request, Provider $provider)
    {
        $provider->update($request->all());
        return self::jsonResponse('success', $provider->refresh());
    }

    /***
     * @param Provider $provider
     * @return false|string
     */
    public function destroy(Provider $provider)
    {
        if ($provider->hasRelations())
            throw ValidationException::withMessages(['id' => trans('validation.has_relations')]);
        $provider->delete();
        return self::jsonResponse('success');
    }
}
