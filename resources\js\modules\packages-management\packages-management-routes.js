const PackagesIndex = () => import ('./packages/index.vue');
const PackagesCreate = () => import ('./packages/create.vue');
const PackagesUpdate = () => import ('./packages/update.vue');
const PackagesDetail = () => import ('./packages/details.vue');


const PackagesManagementRoutes = [
    {
        path: '/packages',
        name: 'packages',
        component: PackagesIndex,
        meta: {
            breadcrumb: 'packages.packages'
        }
    },
    {
        path: '/packages/create',
        name: 'packages/create',
        component: PackagesCreate,
        meta: {
            breadcrumb: 'packages.add'
        }
    },
    {
        path: "/packages/:id/update",
        name: 'packages/update',
        component: PackagesUpdate,
        props: true,
        meta: {
            breadcrumb: 'packages.edit'
        }
    },
    {
        path: "/packages/:id/details",
        name: 'packages/details',
        component: PackagesDetail,
        props: true,
        meta: {
            breadcrumb: 'packages.details'
        }
    },
];


export default PackagesManagementRoutes;
