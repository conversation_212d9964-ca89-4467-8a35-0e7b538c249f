<?php

namespace App\Services;

use App\Enums\ClientNotificationsStatusEnum;
use App\Enums\ClientTypesEnum;

class ClientNotificationService extends BaseService
{
    public function scoutSearch($query, $request)
    {
        $query = $query
        ->query(function ($subQuery) use ($request) {
            $subQuery->with('client');
            if (!$request->notification_id) {
               $subQuery = $subQuery->query(function ($q) use ($request) {
                   $q->where('notification_id', $request->notification_id);
               });
           }

           if($request->search){
               $status = ClientNotificationsStatusEnum::getCaseFromEnumData($request->search);
               if($status)
               $subQuery = $subQuery->where('status', $status);
               $subQuery = $subQuery->orWhereHas('client', function ($q) use ($request) {
               $type = ClientTypesEnum::getCaseFromEnumData($request->search);
               $q->where('username', 'like', '%' . $request->search . '%')
                   ->orWhere('email', 'like', '%' . $request->search . '%');
                   $q->orWhere('type', 'like', '%' . $type . '%');
                   
               });
           }
           
        });

        return $query;
    }
}