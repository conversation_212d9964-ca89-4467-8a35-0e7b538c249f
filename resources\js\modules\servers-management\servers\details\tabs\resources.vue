<template>
    <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
        <t-data-table
            :rows="resourcesTableData"
            :filter="false"
            :pagination="resourcesPagination"
            :query="resourcesQuery"
            :loading="resourcesIsLoading"
            :queryType="`Load${props.id}ResourcesData`"
            :userPermissions="userPermissions"
            :cols="resourcesCols"
            :actions="resourcesActions"
            @loadData="loadResourcesData"
        >
        </t-data-table>
    </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue';
import TDataTable from "@/shared/components/t-data-table.vue";
import serverResourcesTableItems from "../../../models/server-resources-table-items.js";
import cookie from "vue-cookies";
import store from "@/store/store.js";

const t = inject('t');
const currentUser = store.state.auth.user;
const userPermissions = currentUser ? currentUser.permissions : [];

const props = defineProps({
    itemData: {
        required: true,
        type: Object
    },
    id: {
        required: true,
        type: String
    }
});

// Local state for resources tab
const resourcesTableData = ref([]);
const resourcesPagination = ref({
    page: 1,
    total: 0,
    per_page: 10
});
const resourcesQuery = ref({
    search: '',
    page: 1,
    sort: '',
    per_page: 10
});
const resourcesIsLoading = ref(false);

const {
    cols: resourcesCols,
    actions: resourcesActions
} = serverResourcesTableItems(t);

// Function to load resources data from the itemData prop
const loadResourcesData = async (query) => {
    resourcesIsLoading.value = true;
    try {
        // Transform the server data into a format suitable for the resources table
        const resources = [
            {
                id: 1,
                resource: 'RAM',
                value: props.itemData.ram,
                unit: 'GB',
                consumption: props.itemData.ram_consumption || '0',
                threshold: props.itemData.ram_threshold
            },
            {
                id: 2,
                resource: 'CPU',
                value: props.itemData.cpu,
                unit: 'Cores',
                consumption: props.itemData.cpu_consumption || '0',
                threshold: props.itemData.cpu_threshold
            },
            {
                id: 3,
                resource: 'Disk',
                value: props.itemData.disk,
                unit: 'GB',
                consumption: props.itemData.disk_consumption || '0',
                threshold: props.itemData.disk_threshold
            }
        ];
        
        resourcesTableData.value = resources;
        
        // Save pagination state if query is provided
        if (query) {
            resourcesPagination.value = {...resourcesPagination.value, page: query.page, total: resources.length};
            cookie.set(`/servers/resources/Load${props.id}ResourcesData`, JSON.stringify({
                pagination: resourcesPagination.value, 
                query: query
            }));
        }
        
        resourcesIsLoading.value = false;
    } catch (error) {
        resourcesIsLoading.value = false;
        console.error(error);
    }
};


</script>
