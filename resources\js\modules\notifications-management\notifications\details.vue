<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("notifications.details") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("notifications.subject") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.subject }}
                </b>
            </v-col>

            <v-col>
                <v-label>
                    {{ $t("notifications.content") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.content }}
                </b>
            </v-col>
        </v-row>
        <v-row class="mt-n3">
            <v-col>
                <v-label>
                    {{ $t("notifications.type") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{  notificationTypeEnumData[itemData.type][`title_${$t("locale.lang")}`] }}
                </b>
            </v-col>

            <v-col>
                <v-label>
                    {{ $t("notifications.group") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{  itemData.group_id ? itemData.group.name : '-' }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col>
                <v-label>
                    {{ $t("notifications.status") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ notificationStatusEnumData[itemData.status][`title_${$t("locale.lang")}`] }}
                </b>
            </v-col>
            <v-col>
                <v-label>
                    {{ $t("notifications.scheduled_at") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.scheduled_at }}
                </b>
            </v-col>
        </v-row>
        <v-row class="mt-4">
            <v-col>

                <v-tabs
                    v-model="tabs"
                    bg-color="primary"
                >
                    <v-tab value="notifications_clients">{{ $t('notifications.clients') }}</v-tab>

                </v-tabs>

                <v-card-text>
                    <v-tabs-window v-model="tabs">
                        <v-tabs-window-item value="notifications_clients">
                            <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
                                <t-data-table
                                    v-if="itemData"
                                    :rows="clientNotifications"
                                    :pagination="clientNotificationsPagination"
                                    :query="clientNotificationsQuery"
                                    :loading="clientNotificationsIsLoading"
                                    :queryType="`Load${props.id}clientNotificationsData`"
                                    :userPermissions="userPermissions"
                                    :cols="NotificationsClientCols"
                                    @loadData="getClientNotifications"
                                >

                                </t-data-table>
                            </div>
                        </v-tabs-window-item>

                    </v-tabs-window>
                </v-card-text>
            </v-col>

        </v-row>

    </v-container>
</template>

<script setup>
import { onMounted,ref } from "vue";
import useNotifications from "../composables/notifications.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import TDataTable from "@/shared/components/t-data-table.vue";
import NotificationsClientTableItems from "@/modules/notifications-management/models/notifications-client-table-items";


const {
    getGroupLabel,
    t,
    getItem,
    query,
    pagination,
    itemData,
    isLoading,
    router,
    userPermissions,
    updateModal,
    cancel,
    valid,
    validation,
    clientNotifications,
    deleteItem,
    getClientNotifications,
    redirect,
    getGroups,
    tabs,
    clientNotificationsQuery,
    clientNotificationsIsLoading,
    clientNotificationsPagination,
    notificationTypeEnumData,
    notificationStatusEnumData
} = useNotifications();


const props = defineProps({
    id: {
        required: true,
        type: String,
    },
});



const {
    cols: NotificationsClientCols,
    actions: NotificationsClientActions
} = NotificationsClientTableItems(t,redirect,deleteItem);


onMounted(async () => {
    itemData.value = props.id
    await getItem(props.id);
    await getGroups();
     getGroupLabel();
});
</script>
