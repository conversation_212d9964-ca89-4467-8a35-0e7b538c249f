<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ServerDownMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    /**
     * Create a new $server instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function build()
    {
        return $this->subject($this->data['subject'])
            ->markdown('mails.server_down')
            ->with([
                'serverName' => $this->data['server_name'],
                'ipAddress' => $this->data['server_ip'],
                'checkedAt' => $this->data['checked_at']
            ]);
    }
}
