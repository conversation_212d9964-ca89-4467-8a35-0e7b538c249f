<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container>
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("protocols.add") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <v-form v-model="valid" v-on:submit.prevent="saveProtocol">
            <v-row class="mtn-5">
                <v-col cols="12" md="6" lg="6">
                    <v-text-field
                        v-model="form.name"
                        :label="$t('protocols.name')"
                        variant="outlined"
                        :rules="validation.name"
                    ></v-text-field>
                </v-col>
                <v-col cols="12" md="6" lg="6">
                    <v-text-field
                        v-model="form.code"
                        :label="$t('protocols.code')"
                        variant="outlined"
                        :rules="validation.code"
                    ></v-text-field>
                </v-col>
            </v-row>

            <v-alert :text="`${$t('protocols.form_note')}!`" type="info" class="mb-5"></v-alert>

            <v-row class="mtn-5">
                <v-col cols="12">
                    <v-textarea
                        v-model="form.template"
                        :label="$t('protocols.template')"
                        variant="outlined"
                        :rules="validation.template"
                        class="json-template"
                    ></v-textarea>
                </v-col>
            </v-row>

            <router-link :to="{ name: 'protocols' }">
                <v-btn
                    :class="'float-' + $t('right')"
                    class="colored-btn-cancel"
                >
                    {{ $t("cancel") }}
                </v-btn>
            </router-link>
            <v-btn
                :class="'float-' + $t('right') + ' colored-btn'"
                type="submit"
            >
                <span class="px-2">{{ $t("save") }}</span>
                <img class="crud-icon" src="@/assets/icons/ic_add_2.svg" />
            </v-btn>
        </v-form>
    </v-container>
</template>

<script setup>
import useProtocols from "../composables/protocols.js";
import { onMounted } from "vue";

import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";

const { isLoading, storeItem, validation, form, valid, router } =
    useProtocols();

onMounted(() => {});

const saveProtocol = async () => {
    await storeItem({ ...form }, "protocols", true);
};
</script>
