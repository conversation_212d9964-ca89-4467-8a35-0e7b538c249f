.modal-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #cececeb5;
}

.modal-body {
    background-color: #fff;
    border-radius: 15px;
    padding: 20px 10px;
    min-width: 450px;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    font-weight: bold ;
    color: black ;
}


.modal-action {
    padding: 5px;
    display: flex;
    flex-direction: row;
    gap: 5px;
    justify-content: left;
}

.modal-header {
    color: #1B2F4D;
    text-align: right;
    font-weight: bold;
}

.model-define {
    padding: 5px;
    color:#acbfd6 ;
    font-weight: bold ;
    font-size: small ;
}
