<?php

namespace App\Http\Controllers\MainControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProtocolRequests\ProtocolIndexRequest;
use App\Http\Requests\ProtocolRequests\StoreProtocolRequest;
use App\Http\Requests\ProtocolRequests\UpdateProtocolRequest;
use App\Models\Protocol;
use App\Http\Resources\ProtocolResource;
use Illuminate\Support\Facades\DB;

class ProtocolController extends Controller
{
    function __construct()
    {
        $this->middleware('permission:protocols', ['only' => ['index']]);
        $this->middleware('permission:protocols/create', ['only' => ['store']]);
        $this->middleware('permission:protocols/update', ['only' => ['update']]);
        $this->middleware('permission:protocols/details', ['only' => ['show']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(ProtocolIndexRequest $request)
    {
        $query = Protocol::search($request->search)->query(function($query) use ($request){
            $query = $this->orderBy($query, $request, 'protocols');
        });
        return ProtocolResource::collection($query->paginate($request->limit, 'page', $request->offset));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProtocolRequest $request)
    {
        $protocol = Protocol::create($request->validated());
        return self::jsonResponse('success', ProtocolResource::make($protocol));
    }

    /**
     * Display the specified resource.
     */
    public function show(Protocol $protocol)
    {
        return self::jsonResponse('success', ProtocolResource::make($protocol));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Protocol $protocol)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProtocolRequest $request, Protocol $protocol)
    {
        $protocol->edit($request);
        return self::jsonResponse('success', ProtocolResource::make($protocol));
    }
}
