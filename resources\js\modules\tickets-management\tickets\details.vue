<template>
    <t-breadcrumbs
        :path="router.currentRoute.value.path"
        :title="
                itemData?.client?.username ?? $t(router.currentRoute.value.meta.breadcrumb)
            "
    >
    </t-breadcrumbs>
    <v-container  v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t('tickets.details') }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-2 mb-3"></v-divider>
        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("tickets.username") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.user_name}}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("tickets.email") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.email }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("tickets.subject") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.subject }}
                </b>
            </v-col>
            </v-row>

        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("tickets.content") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.content }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("tickets.status") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ ticketStatusEnumData[itemData.status] ? ticketStatusEnumData[itemData.status][`title_${$t("locale.lang")}`] : itemData.status }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("tickets.submitted_at") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.submitted_at }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("tickets.note") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.note }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n5">
            <v-col>
                <v-label>
                    {{ $t("tickets.notes_at") }}
                </v-label>
            </v-col>
            <v-col>
                <b>
                    {{ itemData.notes_at }}
                </b>
            </v-col>
        </v-row>

    </v-container>
</template>

<script setup>

import {onMounted} from "vue";
import useTickets from "@/modules/tickets-management/composables/tickets";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";

const { getItem, itemData, isLoading, router, ticketStatusEnumData } = useTickets()
const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

onMounted( async () => {
    await getItem(props.id)
    })
</script>


