<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum PackageStatusEnum: string
{
    use EnumTrait;

    case Available        = 'available';
    case NOT_Available    = 'not_available';

    const ENUM_DATA = [
        self::Available->value => [
            'title_en' => 'Available',
            'title_ar' => 'متاح',
            'value' => 'available',
        ],
        self::NOT_Available->value => [
            'title_en' => 'Not Available',
            'title_ar' => 'غير متاح',
            'value' => 'not_available',
        ],
    ];

    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }
}
