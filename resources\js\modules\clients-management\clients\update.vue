<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container v-if="!isLoading" class="font-weight-bold">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("clients.edit") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        
        <v-form
                v-model="valid"
                @submit.prevent="save(valid)"
            >
        <v-row>
            
            <v-col cols="12" md="6" lg="6">
                <v-text-field
                    dense
                    v-model="itemData.email"
                    class="required"
                    :label="$t('clients.email')"
                    variant="outlined"
                    :rules="validation.email"
                >
                </v-text-field>
            </v-col>
            
            <v-col cols="12" md="6" lg="6">
                <v-text-field
                    dense
                    type="password"
                    v-model="itemData.password"
                    :label="$t('clients.password')"
                    variant="outlined"
                    :rules="itemData.type === clientTypesCases.GUEST ?
                     [ validationRules.required ,...validation.password] : validation.password"
                >
                </v-text-field>
            </v-col>
            
            <v-col cols="12" md="6" lg="6">
                <v-select
                    dense
                    v-model="itemData.type"
                    class="required"
                    item-value="value"
                    :item-title="`title_${$t('locale.lang')}`"
                    :items="Object.values(clientTypesEnumData)"
                    :label="$t('clients.type')"
                    variant="outlined"
                    :rules="validation.type"
                >
                </v-select>
            </v-col>
            
            <v-col cols="12" md="6" lg="6">
                <v-select
                    dense
                    v-model="itemData.status"
                    class="required"
                    item-value="value"
                    :item-title="`title_${$t('locale.lang')}`"
                    :items="Object.values(clientStatusEnumData)"
                    :label="$t('clients.status')"
                    variant="outlined"
                    :rules="validation.status"
                >
                </v-select>
            </v-col>
            
            <v-col cols="12" md="6" lg="6">
                <v-checkbox
                    dense
                    v-model="itemData.is_verified"
                    :false-value="0"
                    :true-value="1"
                    :label="$t('clients.is_verified')"
                    variant="outlined"
                    :rules="validation.is_verified"
                >
                </v-checkbox>
            </v-col>

            
            <v-col cols="12" md="6" lg="6">
                <v-text-field
                    dense
                    type="datetime-local"
                    v-model="itemData.registered_at_formatted"
                    :label="$t('clients.registered_at')"
                    variant="outlined"
                    :rules="validation.registered_at"
                >
                </v-text-field>
            </v-col>

        </v-row>

        <router-link :to="{ name: 'clients' }">
                <v-btn
                    :class="'float-' + $t('right')"
                    class="colored-btn-cancel"
                >
                    {{ $t("cancel") }}
                </v-btn>
            </router-link>
            <v-btn
                :class="'float-' + $t('right') + ' colored-btn'"
                type="submit"
            >
                <span class="px-2">{{ $t("save") }}</span>
                <img class="crud-icon" src="@/assets/icons/ic_add_2.svg" />
            </v-btn>

        </v-form>
    </v-container>
</template>
<script setup>
import useClients from "../composables/clients.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import {onMounted} from "vue";

const {
    clientTypesCases,
    validationRules,
    clientTypesEnumData,
    clientStatusEnumData,
    isLoading,
    getItem,
    validation,
    itemData, valid,
    router,
    updateItem,
    userPermissions,
} = useClients();

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

onMounted( async () => {
    
    await getItem(props.id, true)

})

const save = async (isValid) => {
    await updateItem({ ...itemData.value }, 'clients')
}

</script>
