<template>
    <v-btn
        @click="reloadData"
        icon="mdi-refresh"
        class="float-end"
    ></v-btn>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
            :reset="true"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container>
        <!-- Servers Section -->
        <v-row>
            <v-col cols="12">
                <v-card
                    class="mb-4"
                    elevation="4"
                    variant="elevated"
                    color="#f1effd"
                >
                <v-card-title>
                                <h5>
                                    <v-icon>mdi mdi-server-network</v-icon>
                                    {{ $t('dashboard.servers.title') }}
                                </h5>
                </v-card-title>
                    <v-card-text>
                        <v-row>
                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="
                                        $t('dashboard.servers.total_server')
                                    "
                                    icon="server"
                                    :value="servers?.total_servers"
                                />
                            </v-col>

                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="
                                        $t('dashboard.servers.active_server')
                                    "
                                    icon="server-network"
                                    :value="servers?.active_servers"
                                />
                            </v-col>

                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="
                                        $t('dashboard.servers.free_servers')
                                    "
                                    icon="currency-usd-off"
                                    :value="servers?.free_servers"
                                />
                            </v-col>

                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="
                                        $t('dashboard.servers.premium_server')
                                    "
                                    icon="currency-usd"
                                    :value="servers?.premium_servers"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>

        <!-- Clients Section -->
        <v-row>
            <v-col cols="12">
                <v-card
                    class="mb-4"
                    elevation="4"
                    variant="elevated"
                    color="#f1effd"
                >
                    <v-card-title>
                        <h5>
                            <v-icon>mdi mdi-account-group</v-icon>
                            {{ $t('dashboard.clients.title') }}
                        </h5>
                    </v-card-title>
                    <v-card-text>
                        <v-row>
                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="$t('statistics.total_users')"
                                    icon="account-group"
                                    :value="clients?.total_users"
                                />
                            </v-col>

                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="$t('statistics.free_users')"
                                    icon="account"
                                    :value="clients?.free_users"
                                />
                            </v-col>

                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="$t('statistics.paid_users')"
                                    icon="account-plus"
                                    :value="clients?.paid_users"
                                />
                            </v-col>

                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="
                                        $t('statistics.new_users_this_month')
                                    "
                                    icon="account-multiple-plus"
                                    :value="clients?.new_users"
                                />
                            </v-col>

                            <v-col cols="12">

                            <!-- Logged in users section -->
                            <v-card
                                class=""
                                elevation="0"
                            >
                            <v-card-title>
                                <h5>
                                    <v-icon>mdi mdi-account-group</v-icon>
                                    {{ $t('statistics.logged_in_clients') }}
                                </h5>
                            </v-card-title>
                                <v-card-text class="pa-0">
                                    <v-row>
                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="$t('statistics.today')"
                                    icon="calendar-today"
                                    :value="clients?.logged_in_users?.today"
                                />
                            </v-col>
                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="$t('statistics.last_7_days')"
                                    icon="calendar-week"
                                    :value="
                                        clients?.logged_in_users?.last_7_days
                                    "
                                />
                            </v-col>
                            <v-col cols="12" md="6" lg="3">
                                <t-statistic-card
                                    :title="$t('statistics.last_30_days')"
                                    icon="calendar-month"
                                    :value="
                                        clients?.logged_in_users?.last_30_days
                                    "
                                />
                            </v-col>
                                    </v-row>

                                    </v-card-text>
                                </v-card>
                            </v-col>



                            <!-- Conversion rate -->
                            <v-col cols="12" md="6" lg="4">
                                <t-statistic-card
                                    :title="
                                        $t('statistics.free_to_paid_conversion')
                                    "
                                    icon="account-convert"
                                    :value="clients?.conversion_rate + '%'"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>

        <!-- Subscriptions Section -->
        <v-row>
            <v-col cols="12">
                <v-card
                    class="mb-4"
                    elevation="4"
                    variant="elevated"
                    color="#f1effd"
                >
                    <v-card-title>
                        <h5>
                            <v-icon>mdi mdi-calendar-month</v-icon>
                            {{ $t('dashboard.subscriptions.title') }}
                        </h5>
                    </v-card-title>
                    <v-card-text>
                        <v-row>
                            <v-col cols="12" sm="6" md="4">
                                <t-statistic-card
                                    :title="
                                        $t(
                                            'dashboard.subscriptions.subscriptions_this_month'
                                        )
                                    "
                                    icon="calendar-month"
                                    :value="subscriptions.this_month"
                                />
                            </v-col>

                            <v-col cols="12" sm="6" md="4">
                                <t-statistic-card
                                    :title="
                                        $t(
                                            'dashboard.subscriptions.subscriptions_last_month'
                                        )
                                    "
                                    icon="calendar-check"
                                    :value="subscriptions.last_month"
                                />
                            </v-col>

                            <v-col cols="12" sm="6" md="4">
                                <t-statistic-card
                                    :title="
                                        $t(
                                            'dashboard.subscriptions.subscriptions_before_last_month'
                                        )
                                    "
                                    icon="timeline-clock-outline"
                                    :value="subscriptions.before_last_month"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>

        <!-- Expenses Section -->
        <v-row>
            <v-col cols="12">
                <v-card
                    class="mb-4"
                    elevation="4"
                    variant="elevated"
                    color="#f1effd"
                >
                    <v-card-title>
                        <h5>
                            <v-icon>mdi mdi-finance</v-icon>
                            {{ $t('dashboard.expenses.title') }}
                        </h5>
                    </v-card-title>
                    <v-card-text>
                        <v-row>
                            <v-col cols="12" sm="6" md="4">
                                <t-statistic-card
                                    :title="$t('dashboard.expenses.total')"
                                    icon="finance"
                                    :value="expenses.total"
                                />
                            </v-col>

                            <v-col cols="12" sm="6" md="4">
                                <t-statistic-card
                                    :title="
                                        $t(
                                            'dashboard.expenses.total_this_month'
                                        )
                                    "
                                    icon="chart-line"
                                    :value="expenses.total_this_month"
                                />
                            </v-col>

                            <v-col cols="12" sm="6" md="4">
                                <t-statistic-card
                                    :title="
                                        $t(
                                            'dashboard.expenses.total_last_month'
                                        )
                                    "
                                    icon="chart-line-variant"
                                    :value="expenses.total_last_month"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>

        <!-- Notifications Section -->
        <v-row>
            <v-col cols="12">
                <v-card
                    class="mb-4"
                    elevation="4"
                    variant="elevated"
                    color="#f1effd"
                >
                    <v-card-title>
                        <h5>
                            <v-icon>mdi mdi-bell</v-icon>
                            {{ $t('dashboard.notifications.title') }}
                        </h5>
                    </v-card-title>
                    <v-card-text>
                        <v-row>
                            <v-col cols="12" sm="6">
                                <t-statistic-card
                                    :title="
                                        $t('dashboard.notifications.sending')
                                    "
                                    icon="calendar-clock"
                                    :value="notifications.sending"
                                />
                            </v-col>

                            <v-col cols="12" sm="6">
                                <t-statistic-card
                                    :title="$t('dashboard.notifications.sent')"
                                    icon="bell-ring"
                                    :value="notifications.sent"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>

        <v-row>
            <v-col>
                <h5>
                    {{
                        $t(
                            "dashboard.subscriptions.number_of_users_per_package"
                        )
                    }}
                </h5>
                <div class="chart-wrap custom-bar">
                    <div v-if="renderBar" id="chart">
                        <VueApexCharts
                            width="100%"
                            height="400"
                            type="bar"
                            :options="chartOptions"
                            :series="chartOptions.series"
                        ></VueApexCharts>
                    </div>
                </div>
            </v-col>
        </v-row>
    </v-container>
</template>

<script setup>
import VueApexCharts from "vue3-apexcharts";
import useShared from "@/helpers/shared.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import TStatisticCard from "@/shared/components/t-statistic-card.vue";
import { onMounted } from "vue";
import useDashboard from "./composables/dashboard";
import { ref } from "vue";

const { router } = useShared();

const {
    refreshDashboard,
    reloadData,

    getClients,
    clients,

    getSubscriptions,
    subscriptions,
    subscriptionsStatics,
    chartOptions,
    renderBar,

    getExpenses,
    expenses,

    getNotifications,
    notifications,

    getServers,
    servers,
} = useDashboard();



onMounted(async () => {
    await refreshDashboard();
});




</script>
