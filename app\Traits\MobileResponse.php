<?php

namespace App\Traits;


use Illuminate\Http\Response;

trait MobileResponse
{
    public static function MobileResponse($message, $data = null, $code = Response::HTTP_OK)
    {
        $response = [
            'message' => trans()->has('messages.' . $message) ? trans('messages.' . $message) : $message,
            'data' => $data
        ];
        return response()->json($response, $code);
    }
}
