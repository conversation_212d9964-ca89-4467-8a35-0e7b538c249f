<?php

namespace App\Jobs;

use App\Helpers\MailHelper;
use App\Mail\ClientTicketMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ReportTicketJob implements ShouldQueue
{
    use Queueable;

    public $ticket;

    /**
     * Create a new job instance.
     */
    public function __construct($ticket)
    {
        $this->ticket = $ticket;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $data = [
            'mailer' => 'alarm',
            'to' => config('mail.mailers.alarm.to.address'),
            'ticket_id' => $this->ticket['id'],
            'subject' => $this->ticket['subject'],
            'body' => $this->ticket['content'],
            'email' => $this->ticket['email']
        ];

        MailHelper::report(new ClientTicketMail($data), $data);
    }
}
