<?php

namespace App\Http\Controllers\MainControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\PortRequests\IndexPortRequest;
use App\Http\Requests\PortRequests\StorePortRequest;
use App\Http\Requests\PortRequests\UpdatePortRequest;
use App\Http\Resources\PortResource;
use App\Http\Resources\ProtocolResource;
use App\Models\Port;
use App\Models\Protocol;
use Illuminate\Validation\ValidationException;

class PortController extends Controller
{

    function __construct()
    {
        $this->middleware('permission:ports', ['only' => ['index']]);
        $this->middleware('permission:ports/create', ['only' => ['store']]);
        $this->middleware('permission:ports/update', ['only' => ['update']]);
        $this->middleware('permission:ports/details', ['only' => ['show']]);
        $this->middleware('permission:ports/delete', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexPortRequest $request)
    {
        $query = Port::search($request->search)
        ->where('server_id', $request->parent_id)
        ->query(function ($subQuery) use ($request) {
            $subQuery->with('protocol');
            $subQuery = $this->orderBy($subQuery, $request, 'ports');
        });
        return PortResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    /**
     * @param StorePortRequest $request
     * @return false|string
     */
    public function store(StorePortRequest $request)
    {
        $port = Port::create($request->validated());
        return self::jsonResponse('success', $port);
    }

    /***
     * @param Port $port
     * @return false|string
     */
    public function show(Port $port)
    {
        $port->load('server','protocol:name,code,id');
        return self::jsonResponse('success', $port);
    }

    /**
     * @param UpdatePortRequest $request
     * @param Port $port
     * @return false|string
     */
    public function update(UpdatePortRequest $request, Port $port)
    {
        $port->update($request->validated());
        return self::jsonResponse('success', null);
    }

    /***
     * @param Port $port
     * @return false|string
     */
    public function destroy(Port $port)
    {
        if($port->hasRelations())
        throw ValidationException::withMessages(['id' => trans('validation.has_relations')]);

        $port->delete();
        return self::jsonResponse('success');
    }

    
    public function protocols()
    {
        $protocols = Protocol::select('id', 'name','code')->get();
        return ProtocolResource::collection($protocols);
    }
}
