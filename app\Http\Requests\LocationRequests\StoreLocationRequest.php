<?php

namespace App\Http\Requests\LocationRequests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'parent_id'   => 'sometimes|exists:locations,id|integer',
            'name'        => ['required', 'string' , 'max:100' ,
                Rule::unique('locations')->whereNull('deleted_at')
            ],
            'code'        => ['required', 'string' ,
                Rule::unique('locations')->whereNull('deleted_at')
            ],
            'free_servers_count' => 'sometimes|integer|min:0',
            'premium_servers_count' => 'sometimes|integer|min:0',
        ];
    }
}
