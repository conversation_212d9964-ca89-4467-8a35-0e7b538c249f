<?php

namespace App\Jobs;

use App\Enums\ServerExternalStatus;
use App\Helpers\MailHelper;
use App\Mail\ServerDownMail;
use App\Models\Server;
use App\Services\ServerService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class CheckServerUpdatesJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         * Get all servers
         */
        $servers = Server::all();

        $fiveMinutesAgo = Carbon::now()->subMinutes(5);

        foreach ($servers as $server) {

            $shouldReport = !$server['last_connection_at'] || Carbon::make($server['last_connection_at'])->lt($fiveMinutesAgo);

            /**
             * Check if the server has connected in the last 5 minutes
             */
            if ($server->isUp() && $shouldReport) {
                /**
                 * Mark the server as down
                 */
                $server->update([
                    'external_status' => ServerExternalStatus::DOWN->value
                ]);

                /**
                 * Report the server is down
                 */
                $data = [
                    'mailer' => 'alarm',
                    'to' => config('mail.mailers.alarm.to.address'),
                    'subject' => "Server Down Alert: {$server->name}",
                    'server_name' => $server->name,
                    'server_ip' => $server->ip,
                    'checked_at' => now()->toDateTimeString()
                ];

                MailHelper::report(new ServerDownMail($data), $data);

            } else if ($server->isDown() && !$shouldReport) {
                /**
                 * Mark the server as up
                 */
                $server->update([
                    'external_status' => ServerExternalStatus::UP->value
                ]);
            }
        }
    }
}
