<?php

namespace App\Http\Resources\ClientNotification;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClientNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                => $this->id,
            'client'            => $this->client->username ?? null,
            'email'             => $this->client->email ?? null,
            'type'              => $this->client->type,
            'sent_at'           => $this->sent_at,
            'status'            => $this->status,
        ];
    }
}
