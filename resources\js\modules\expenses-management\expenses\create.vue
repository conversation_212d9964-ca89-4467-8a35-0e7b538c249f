<template>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container class="font-weight-bold">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("expenses.add") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>


        <v-form
                v-model="valid"
                @submit.prevent="save"
            >
            <v-row>
            
            <v-col cols="12" md="6" lg="6">
                <v-text-field
                    dense
                    type="number"
                    step="any"
                    v-model="form.amount"
                    class="required"
                    :label="$t('expenses.amount')"
                    variant="outlined"
                    :rules="validation.amount"
                    suffix="$"
                    >
                </v-text-field>

            </v-col>
            
            <v-col cols="12" md="6" lg="6">
            <v-text-field
                    dense
                    type="date"
                    v-model="form.payment_date"
                    class="required"
                    :label="$t('expenses.payment_date')"
                    variant="outlined"
                    :rules="validation.payment_date"
                >
                </v-text-field>
            </v-col>

            <v-col cols="12" md="6" lg="6">
                <v-select
                    v-model="form.type"
                        :items="Object.values(expenseTypes().enumData)"
                        item-value="value"
                        :item-title="`title_${$t('locale.lang')}`"
                        variant="outlined"
                        :label="$t('expenses.type')"
                        :rules="validation.type"
                ></v-select>
            </v-col>

            <v-col cols="12" md="6" lg="6" v-if="form.type === expenseTypes().cases.SERVER">
                    <v-autocomplete
                        v-model="form.server_id"
                        :items="servers"
                        item-value="id"
                        item-title="name"
                        :label="$t('expenses.server')"
                        @click="getServers()"
                        variant="outlined"
                        dense
                        :rules="validation.server_id"
                        ></v-autocomplete>
            </v-col>


            <v-col cols="12">
            <v-textarea
                    dense
                    v-model="form.description"
                    class="required"
                    :label="$t('expenses.description')"
                    variant="outlined"
                    :rules="validation.description"
                >
                </v-textarea>
            </v-col>

        </v-row>

        <router-link :to="{ name: 'expenses' }">
                <v-btn
                    :class="'float-' + $t('right')"
                    class="colored-btn-cancel"
                >
                    {{ $t("cancel") }}
                </v-btn>
            </router-link>
            <v-btn
                :class="'float-' + $t('right') + ' colored-btn'"
                type="submit"
            >
                <span class="px-2">{{ $t("save") }}</span>
                <img class="crud-icon" src="@/assets/icons/ic_add_2.svg" />
            </v-btn>

        </v-form>
    </v-container>
</template>
<script setup>
import useExpenses from "../composables/expenses.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";

const {
    isLoading,
    expenseTypes,
    storeItem,
    validation,
    form,
    valid,
    router,
    getServers,
    servers,
    notify,
    t,
    userPermissions,
} = useExpenses();


const save = async () => {
    await storeItem({ ...form }, 'expenses')
}


</script>
