<?php

namespace App\Http\Requests\ExpenseRequests;

use App\Enums\ExpenseTypesEnum;
use App\Http\Requests\BaseRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateExpenseRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'server_id' => ['required_if:type,'.ExpenseTypesEnum::SERVER->value, 'exists_ignore_deleted:servers,id', 'nullable'],
            'description' => ['required', 'string'],
            'amount' => ['required', 'numeric', 'min:0.01'],
            'type' => ['required', Rule::in(ExpenseTypesEnum::getValues())],
            'payment_date' => ['required', 'date'],
        ];
    }
}
