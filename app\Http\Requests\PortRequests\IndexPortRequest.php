<?php

namespace App\Http\Requests\PortRequests;

use App\Http\Requests\BaseRequest;

class IndexPortRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'limit' =>'required',
             'page' =>'required',
        ];
        $rules['parent_id'] = ['required', 'min:1', 'integer', 'exists_ignore_deleted:servers,id'];
        return $rules;
    }
}
