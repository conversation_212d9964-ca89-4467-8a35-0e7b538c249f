<?php

namespace App\Helpers;

use App\Enums\EmailReportStatusEnum;
use App\Jobs\SendMailJob;
use App\Models\EmailLog;
use App\Services\EmailLogService;
use Exception;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\Mail;

class MailHelper
{
    public static function send(string $email, Mailable $mailable, string $mailer = '')
    {
        $mail = $mailer ? Mail::mailer($mailer): Mail::to($email);
        $mail->send($mailable);
        return true;
    }

    public static function queue(string $email, Mailable $mailable, string $mailer = '')
    {
        SendMailJob::dispatch($email, $mailable, $mailer);
        return true;
    }

    public static function report(Mailable $mailable, $data)
    {
        try {
            self::send($data['to'], $mailable, $data['mailer'] ?? '');

            $data['status'] = EmailReportStatusEnum::Sent->value;

            /* Log the email as sent */
            (new EmailLogService)->store($data);
            return true;

        } catch (Exception $e) {
            $data['status'] = EmailReportStatusEnum::Failed->value;

            /* Log the email as failed */
            (new EmailLogService)->store($data);

            throw $e;
            
        }
    }
}
