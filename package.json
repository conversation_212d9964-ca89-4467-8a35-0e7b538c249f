{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@mdi/font": "^7.2.96", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "vite": "^5.0"}, "dependencies": {"@intlify/unplugin-vue-i18n": "^6.0.0", "@jobinsjp/vue3-datatable": "^1.0.15", "@kyvg/vue3-notification": "^3.4.1", "@vitejs/plugin-vue": "^5.2.1", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^13.2.0", "apexcharts": "^4.7.0", "material-design-icons-iconfont": "^6.7.0", "vue": "^3.5.13", "vue-axios": "^3.5.2", "vue-cookies": "^1.8.4", "vue-i18n": "^10.0.5", "vue-json-excel3": "^1.0.30", "vue-loader": "^17.4.2", "vue-router": "^4.5.0", "vue3-apexcharts": "^1.8.0", "vuejs-confirm-dialog": "^0.5.2", "vuetify": "^3.7.5", "vuex": "^4.1.0"}}